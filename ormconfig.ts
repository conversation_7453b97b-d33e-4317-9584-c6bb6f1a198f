import './src/boilerplate.polyfill';

import dotenv from 'dotenv';
import { DataSource } from 'typeorm';

import { SnakeNamingStrategy } from './src/snake-naming.strategy';

dotenv.config();

export default new DataSource({
  type: 'postgres',
  url: process.env.DB_URL,
  entities: ['src/modules/**/*.entity{.ts,.js}'],
  migrations: ['src/databases/migrations/*{.ts,.js}'],
  namingStrategy: new SnakeNamingStrategy(),
});
