{"language": "ts", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"plugins": [{"name": "@nestjs/swagger/plugin", "options": {"dtoFileNameSuffix": [".dto.ts"], "controllerFileNameSuffix": [".controller.ts"], "classValidatorShim": true, "introspectComments": true}}], "assets": [{"include": "**/*.proto", "outDir": "./dist/src/", "watchAssets": true}, {"include": "**/*.hbs", "outDir": "./dist/src/", "watchAssets": true}], "watchAssets": true}}