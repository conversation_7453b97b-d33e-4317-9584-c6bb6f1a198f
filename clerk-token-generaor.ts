// src/generate-token.ts
import 'dotenv/config';
import { createClerkClient } from '@clerk/backend';

async function generateToken(): Promise<void> {
  const secretKey = process.env.CLERK_SECRET_KEY;
  if (!secretKey) {
    throw new Error('Missing CLERK_SECRET_KEY in environment variables.');
  }

  const clerk = createClerkClient({ secretKey });
  const userId = 'user_2zhVdxuI4VoNhlcWnFVs6vm3XmN';
  const jwtTemplateName = 'khodkar-core';

  try {
    // Step 1: Create a session
    const session = await clerk.sessions.createSession({ userId });
    console.log('✅ Session ID:', session.id);

    // Step 2: Generate a JWT from the session
    const tokenResponse = await clerk.sessions.getToken(session.id, jwtTemplateName);
    console.log('✅ JWT token:\n', tokenResponse.jwt);
  } catch (err: any) {
    console.error('❌ Failed to generate token or retrieve JWT:', err);
  }
}

generateToken();
