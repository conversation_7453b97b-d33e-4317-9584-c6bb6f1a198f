{"name": "khodkar-core", "version": "1.0.0", "description": "", "author": "zigBalthazar <<EMAIL>>", "private": true, "license": "UNLICENSED", "type": "commonjs", "scripts": {"build": "nest build", "build:prod": "tsc -p tsconfig.build.json", "start:dev": "ts-node src/main.ts", "start:prod": "node dist/src/main.js", "new": "hygen new", "watch:dev": "ts-node-dev src/main.ts", "debug:dev": "cross-env TS_NODE_CACHE=false ts-node-dev --inspect --ignore '/^src/.*\\.spec\\.ts$/' src/main.ts", "lint": "eslint . --ext .ts", "lint:fix": "eslint --fix . --ext .ts", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:cov": "NODE_ENV=test jest --coverage", "test:debug": "NODE_ENV=test node --inspect-brk -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=test jest --config ./test/jest-e2e.json", "docs:dev": "vuepress dev -p 7070", "prepare": "husky install", "release": "release-it", "proto:win": "protoc --plugin=protoc-gen-ts_proto=.\\node_modules\\.bin\\protoc-gen-ts_proto.cmd   --ts_proto_opt=esModuleInterop=true --ts_proto_opt=nestJs=true --ts_proto_opt=addGrpcMetadata=true -I=./src/modules/grpc/proto src/modules/grpc/proto/*.proto --ts_proto_out=./src/modules/grpc/gen/ts", "proto:linux": "protoc --plugin=node_modules/ts-proto/protoc-gen-ts_proto --ts_proto_opt=nestJs=true  --ts_proto_opt=esModuleInterop=true --ts_proto_opt=addGrpcMetadata=true --experimental_allow_proto3_optional -I=./src/modules/grpc/proto src/modules/grpc/proto/*.proto  --ts_proto_out=./src/modules/grpc/gen/ts", "migration:generate": "ts-node ./node_modules/typeorm/cli.js migration:generate -d ormconfig.ts", "migration:create": "ts-node ./node_modules/typeorm/cli.js migration:create -d ormconfig.ts", "migration:revert": "ts-node ./node_modules/typeorm/cli.js migration:revert -d ormconfig.ts"}, "dependencies": {"@ai-sdk/openai-compatible": "^0.2.14", "@asteasolutions/zod-to-openapi": "^7.3.4", "@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.842.0", "@clerk/backend": "^2.1.0", "@grpc/grpc-js": "^1.12.2", "@grpc/proto-loader": "^0.7.13", "@grpc/reflection": "^1.0.4", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.7", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/schematics": "^11.0.5", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^7.1.17", "@nestjs/terminus": "^10.2.3", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^10.0.2", "@opentelemetry/auto-instrumentations-node": "^0.60.1", "@opentelemetry/sdk-node": "^0.202.0", "@qdrant/js-client-rest": "^1.14.1", "@types/nunjucks": "^3.2.6", "ai": "^4.3.16", "axios": "^1.10.0", "bcrypt": "^5.1.1", "cache-manager-redis-store": "^3.0.1", "class-transformer": "~0.5.1", "class-validator": "~0.14.0", "compression": "^1.7.4", "discord.js": "^14.21.0", "express": "^4.18.2", "fastembed": "^1.14.4", "handlebars": "^4.7.8", "helmet": "^7.1.0", "ioredis": "^5.4.1", "isomorphic-ws": "^5.0.0", "jsonwebtoken": "^9.0.2", "langfuse-vercel": "^3.37.4", "lodash": "^4.17.21", "mime-types": "^2.1.35", "morgan": "^1.10.0", "multer": "^2.0.1", "necord": "^6.8.14", "nestjs-cls": "^3.6.0", "nestjs-grpc-exceptions": "^0.2.2", "nestjs-i18n": "^10.4.0", "nestjs-typeorm-paginate": "^4.1.0", "nodemailer": "^7.0.4", "nunjucks": "^3.2.4", "parse-duration": "^2.1.3", "passport": "^0.7.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-strategy": "^1.0.0", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "source-map-support": "^0.5.21", "svix": "^1.67.0", "swagger-themes": "^1.4.3", "swagger-ui-express": "^5.0.0", "ts-proto": "^2.2.7", "tslib": "^2.6.2", "typeorm": "^0.3.25", "typeorm-transactional": "^0.5.0", "uuid": "^9.0.1", "ws": "^8.18.0", "zod": "^3.25.67"}, "devDependencies": {"@clerk/clerk-sdk-node": "^5.1.6", "@handfish/hygen": "^6.1.6", "@nestjs/cli": "^10.2.1", "@nestjs/testing": "^10.3.0", "@types/bcrypt": "^5.0.2", "@types/cache-manager-redis-store": "^2.0.4", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.9", "@types/multer": "^2.0.0", "@types/node": "^20.10.6", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-canonical": "^4.18.0", "eslint-plugin-deprecation": "^3.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-import-helpers": "^1.3.1", "eslint-plugin-n": "^16.6.1", "eslint-plugin-no-secrets": "^1.0.2", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-sonarjs": "^0.23.0", "eslint-plugin-unicorn": "^50.0.1", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "~15.2.0", "prettier": "^3.1.1", "release-it": "^17.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}