<!DOCTYPE html>
<html>
<head>
  <title>Gemini Voice Chat</title>
</head>
<body>
  <h1>🎙 Talk to <PERSON></h1>
  <button onclick="start()">Start</button>
  <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
  <script>
    const socket = io('http://localhost:3000'); // Adjust port if needed

    async function start() {
      const audioContext = new AudioContext({ sampleRate: 16000 });
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const source = audioContext.createMediaStreamSource(stream);

      const processor = audioContext.createScriptProcessor(4096, 1, 1);
      source.connect(processor);
      processor.connect(audioContext.destination);

      processor.onaudioprocess = (e) => {
        const input = e.inputBuffer.getChannelData(0);
        const int16 = new Int16Array(input.length);
        for (let i = 0; i < input.length; i++) {
          int16[i] = Math.min(1, input[i]) * 0x7fff;
        }
        socket.emit('mic_audio', int16);
      };
    }

    socket.on('gemini_audio', async (data) => {
      const blob = new Blob([data], { type: 'audio/wav' });
      const url = URL.createObjectURL(blob);
      const audio = new Audio(url);
      audio.play();
    });
  </script>
</body>
</html>
