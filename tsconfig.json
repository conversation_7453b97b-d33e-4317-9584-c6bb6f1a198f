{"compilerOptions": {"module": "commonjs", "declaration": false, "noImplicitAny": true, "removeComments": true, "noLib": false, "skipLibCheck": true, "importHelpers": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "esModuleInterop": true, "allowUnreachableCode": false, "noImplicitReturns": true, "strictNullChecks": true, "noImplicitThis": true, "noUnusedParameters": true, "noUnusedLocals": false, "target": "ES2022", "noFallthroughCasesInSwitch": true, "sourceMap": true, "noEmitHelpers": true, "noUncheckedIndexedAccess": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "paths": {}}, "exclude": ["node_modules", "**/*.spec.ts"]}