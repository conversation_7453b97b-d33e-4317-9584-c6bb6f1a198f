export function getVariableName<TResult>(getVar: () => TResult): string | undefined {
  const m = /\(\)=>(.*)/.exec(getVar.toString().replaceAll(/(\r\n|\n|\r|\s)/gm, ''));

  if (!m) {
    throw new Error("The function does not contain a statement matching 'return variableName;'");
  }

  const fullMemberName = m[1]!;

  const memberParts = fullMemberName.split('.');

  return memberParts.at(-1);
}

export function replaceGetManyFilter(q: string[] | undefined, propKey: string, replaceWith: string): string[] {
  if (!q) q = [];

  const index = q.findIndex((i) => {
    if (typeof i !== 'string') return false;
    return i.split(':')[0] === propKey;
  });

  if (index >= 0) {
    q[index] = replaceWith;
  } else {
    q.push(replaceWith);
  }

  return q as string[];
}
