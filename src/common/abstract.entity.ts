/* eslint-disable max-classes-per-file */
import { CreateDateColumn, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

import type { Constructor } from '../types';
import type { AbstractDto } from './dto/abstract.dto';

export interface IAbstractEntity<DTO extends AbstractDto> {
  id: string;
  createdAt: Date;
  updatedAt: Date;

  toDto(): DTO;
}

export abstract class AbstractEntity<DTO extends AbstractDto = AbstractDto> implements IAbstractEntity<DTO> {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn({
    type: 'timestamp',
  })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
  })
  updatedAt: Date;

  dtoClass: Constructor<DTO, [AbstractEntity]>;

  toDto(): DTO {
    const dtoClass = this.dtoClass;

    if (!dtoClass) {
      throw new Error(`You need to use @UseDto on class be able to call toDto function`);
    }

    return new dtoClass(this);
  }

  constructor(item?: Partial<AbstractEntity>) {
    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<AbstractEntity>) {
    const { id: id, createdAt, updatedAt } = item;

    if (id) {
      this.id = id;
    }

    this.createdAt = createdAt ? new Date(createdAt) : this.createdAt;
    this.updatedAt = updatedAt ? new Date(updatedAt) : this.updatedAt;
  }
}
