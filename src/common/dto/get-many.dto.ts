import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsInt, Min, IsArray, IsString } from 'class-validator';

export class GetManyDto<T> {
  @ApiPropertyOptional({
    type: String,
    description: 'Comma-separated list of fields to include. Example: "id,name,status"',
  })
  @Transform(({ value }) => {
    if (typeof value !== 'string') return [];
    return value
      .split(',')
      .map((v) => v.trim())
      .filter(Boolean);
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  select?: (keyof T)[];

  @ApiPropertyOptional({
    type: String,
    description: 'Comma-separated list of relations to join. Example: "messages,transport"',
  })
  @Transform(({ value }) => {
    if (typeof value !== 'string') return [];
    return value
      .split(',')
      .map((v) => v.trim())
      .filter(Boolean);
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  join?: (keyof T)[];

  @ApiPropertyOptional({
    type: String,
    description: `AND-based filters in 'field:operator:value' format. Example: "status:eq:CLOSED,id:eq:abc-123"`,
  })
  @Transform(({ value }) => {
    if (typeof value !== 'string') return [];
    return value
      .split(',')
      .map((v) => v.trim())
      .filter(Boolean);
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  filter?: string[];

  @ApiPropertyOptional({
    type: String,
    description: `OR-based filters in 'field:operator:value' format. Example: "status:eq:OPEN,status:eq:PENDING"`,
  })
  @Transform(({ value }) => {
    if (typeof value !== 'string') return [];
    return value
      .split(',')
      .map((v) => v.trim())
      .filter(Boolean);
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  filterOr?: string[];

  @ApiPropertyOptional({
    type: String,
    description: `Sorting format: "field:direction". Direction: ASC or DESC. Example: "createdAt:DESC,name:ASC"`,
  })
  @Transform(({ value }) => {
    if (typeof value !== 'string') return [];
    return value
      .split(',')
      .map((v) => v.trim())
      .filter(Boolean);
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  sort?: string[];

  @ApiPropertyOptional({
    type: Number,
    description: 'Page number for pagination. Starts at 1.',
    minimum: 1,
  })
  @Transform(({ value }) => {
    const n = parseInt(value, 10);
    return isNaN(n) ? undefined : n;
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({
    type: Number,
    description: 'Number of items per page. Minimum 1.',
    minimum: 1,
  })
  @Transform(({ value }) => {
    const n = parseInt(value, 10);
    return isNaN(n) ? undefined : n;
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  limit?: number;
}
