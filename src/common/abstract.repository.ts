import { Injectable, NotFoundException } from '@nestjs/common';
import {
  Brackets,
  type DeepPartial,
  type FindManyOptions,
  type FindOneOptions,
  type FindOptionsWhere,
  type ObjectLiteral,
  type Repository,
} from 'typeorm';
import { GetManyDto } from './dto/get-many.dto';
import { AbstractDto } from './dto/abstract.dto';
import { PageMetaDto } from './dto/page-meta.dto';
import { getManyResult } from './dto/page.dto';

type Op = 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'in';
const opMap: Record<Op, string> = {
  eq: '=',
  ne: '!=',
  gt: '>',
  gte: '>=',
  lt: '<',
  lte: '<=',
  like: 'LIKE',
  in: 'IN',
};

export type GetManyDtoOptions<T> = {
  selectables?: (keyof T & string)[];
  joinables?: (keyof T & string)[];
  filterables?: (keyof T & string)[];
  sortables?: (keyof T & string)[];
  toDto?: boolean;
};

@Injectable()
export abstract class AbstractRepository<T extends ObjectLiteral> {
  constructor(protected readonly repository: Repository<T>) {}

  public get repo(): Repository<T> {
    return this.repository;
  }

  /**
   * Find all entities with optional filtering.
   * @param options - Query options
   */
  async findAll(options?: FindManyOptions<T>): Promise<T[]> {
    return this.repository.find({ ...options });
  }

  /**
   * Find one entity based on criteria.
   * @param options - Query options
   */
  async findOne(options: FindOneOptions<T>): Promise<T | null> {
    return this.repository.findOne({ ...options });
  }

  /**
   * Find an entity by its ID.
   * @param id - ID of the entity
   */
  async findById(id: string | number): Promise<T | null> {
    return this.repository.findOne({
      where: { id } as unknown as FindOptionsWhere<T>, // Cast the where clause to match the entity type
    });
  }

  /**
   * Save a single entity or multiple entities.
   * @param data - Entity or entities to save
   */
  async save(data: DeepPartial<T>): Promise<T> {
    return this.repository.save(data);
  }

  /**
   * Delete an entity by ID.
   * @param id - ID of the entity to delete
   */
  async deleteById(id: string | number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Count entities matching given criteria.
   * @param options - Query options
   */
  async count(options?: FindManyOptions<T>): Promise<number> {
    return this.repository.count({ ...options });
  }

  /**
   * Soft delete an entity by ID (if entity supports soft deletes).
   * @param id - ID of the entity
   */
  async softDeleteById(id: string | number): Promise<void> {
    await this.repository.softDelete(id);
  }

  /**
   * Restore a soft-deleted entity by ID.
   * @param id - ID of the entity
   */
  async restoreById(id: string | number): Promise<void> {
    await this.repository.restore(id);
  }

  async findOrFail(options: FindOneOptions<T>): Promise<T> {
    const entity = await this.findOne(options);
    if (!entity) {
      throw new NotFoundException(`${this.repository.metadata.name.replaceAll('Entity','')} not found`);
    }
    return entity;
  }

  create(entityLike?: DeepPartial<T>) {
    if (entityLike) {
      return this.repository.create(entityLike);
    }
    return this.repository.create();
  }

  async getMany<T>(q: GetManyDto<T>, options: GetManyDtoOptions<T>): Promise<getManyResult<T>> {
    const qb = this.repo.createQueryBuilder('e');

    // JOIN
    for (const rel of q.join ?? []) {
      if (options.joinables) {
        if (!options.joinables.includes(rel as keyof T & string)) throw new Error(`Invalid join: ${String(rel)}`);
      }
      qb.leftJoinAndSelect(`e.${String(rel)}`, String(rel));
    }

    // SELECT
    if (q.select) {
      if (options.selectables) {
        for (const sel of q.select) {
          if (!options.selectables.includes(sel as keyof T & string)) throw new Error(`Invalid select: ${String(sel)}`);
        }
      }
      qb.select(q.select.map((s) => `e.${String(s)}`));
    }

    // FILTER (AND only)
    const filters = q.filter ?? [];
    for (let i = 0; i < filters.length; i++) {
      const filter = filters[i] ?? '';
      const parts = filter.split(':');
      if (parts.length !== 3) throw new Error(`Malformed filter: ${filter}`);
      const [field, op, raw] = parts as [string, Op, string];

      if (options.filterables && !options.filterables.includes(field as keyof T & string)) {
        throw new Error(`Invalid filter field: ${field}`);
      }

      if (!(op in opMap)) throw new Error(`Invalid operator: ${op}`);

      const alias = `f_${i}`;
      const col = field.includes('.') ? field : `e.${field}`;

      if (op === 'in') {
        const list = raw.split(',').map((x) => x.trim());
        qb.andWhere(`${col} IN (:...${alias})`, { [alias]: list });
      } else {
        const value = op === 'like' ? `%${raw}%` : raw;
        qb.andWhere(`${col} ${opMap[op]} :${alias}`, { [alias]: value });
      }
    }

    // FILTER (OR only)
    const filterOr = q.filterOr ?? [];
    if (filterOr.length > 0) {
      qb.andWhere(
        new Brackets((orQ) => {
          for (let i = 0; i < filterOr.length; i++) {
            const filter = filterOr[i] ?? '';
            const parts = filter.split(':');
            if (parts.length !== 3) throw new Error(`Malformed filter: ${filter}`);
            const [field, op, raw] = parts as [string, Op, string];

            if (options.filterables && !options.filterables.includes(field as keyof T & string)) {
              throw new Error(`Invalid filter field: ${field}`);
            }

            if (!(op in opMap)) throw new Error(`Invalid operator: ${op}`);

            const alias = `or_f_${i}`;
            const col = field.includes('.') ? field : `e.${field}`;

            if (op === 'in') {
              const list = raw.split(',').map((x) => x.trim());
              orQ.orWhere(`${col} IN (:...${alias})`, { [alias]: list });
            } else {
              const value = op === 'like' ? `%${raw}%` : raw;
              orQ.orWhere(`${col} ${opMap[op]} :${alias}`, { [alias]: value });
            }
          }
        }),
      );
    }

    // SORT
    for (const s of q.sort ?? []) {
      const parts = s.split(':');
      if (parts.length !== 2) throw new Error(`Malformed sort: ${s}`);
      const [field, dir] = parts;
      if (options.sortables) {
        if (field && !options.sortables.includes(field as keyof T & string))
          throw new Error(`Invalid sort field: ${field}`);
      }
      if (['ASC', 'DESC'].includes(dir!.toUpperCase())) {
        qb.addOrderBy(`e.${field}`, dir!.toUpperCase() as 'ASC' | 'DESC');
      } else {
        throw new Error(`Invalid dir: ${dir}`);
      }
    }

    // PAGINATION
    if (q.page && q.limit) {
      const skip = (q.page - 1) * q.limit;
      qb.skip(skip).take(q.limit);
    }

    const [items, total] = await qb.getManyAndCount();

    return {
      data: (options.toDto ? items.toDtos() : items) as T[],
      meta: { total, page: q.page ?? 0, limit: q.limit ?? 0 },
    };
  }
}
