import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  ListBucketsCommand,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Injectable } from '@nestjs/common';
import { ApiConfigService } from './api-config.service';
import { Readable } from 'stream';

@Injectable()
export class SpaceService {
  private s3Client: S3Client;
  private bucket: string;

  constructor(private configService: ApiConfigService) {
    this.bucket = this.configService.s3Config.bucket;

    this.s3Client = new S3Client({
      endpoint: this.configService.s3Config.endpoint,
      region: 'eu',
      credentials: {
        accessKeyId: this.configService.s3Config.accessKeyId,
        secretAccessKey: this.configService.s3Config.secretAccessKey,
      },
      forcePathStyle: true,
    });
  }

  async uploadFile(key: string, content: Buffer | Readable, contentType: string) {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: content,
      ContentType: contentType,
    });
    await this.s3Client.send(command);
    return { key };
  }

  async downloadFile(key: string): Promise<Readable> {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });
    const response = await this.s3Client.send(command);
    return response.Body as Readable;
  }

  async getSignedUrl(key: string, expiresInSeconds = 3600): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });
    return await getSignedUrl(this.s3Client, command, { expiresIn: expiresInSeconds });
  }

  async deleteFile(key: string) {
    const command = new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });
    await this.s3Client.send(command);
    return { deleted: true };
  }

  async getListBucket() {
    const command = new ListBucketsCommand({});
    return this.s3Client.send(command);
  }
}
