import { MigrationInterface, QueryRunner } from "typeorm";

export class Init1752054881229 implements MigrationInterface {
    name = 'Init1752054881229'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."prompts_type_enum" AS ENUM('DATA_EXTRACTOR', 'COSTUMER_SUPPORT', 'DEFAULT_CUSTOMER_SUPPORT')`);
        await queryRunner.query(`CREATE TABLE "prompts" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "type" "public"."prompts_type_enum" NOT NULL, "content" character varying NOT NULL, "setting" jsonb NOT NULL DEFAULT '{}', "user_id" uuid NOT NULL, CONSTRAINT "PK_21f33798862975179e40b216a1d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "tools" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, "description" character varying NOT NULL, "setting" jsonb NOT NULL DEFAULT '{}', "user_id" uuid NOT NULL, "is_public" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_e23d56734caad471277bad8bf85" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."messages_role_enum" AS ENUM('user', 'assistant', 'tool', 'system')`);
        await queryRunner.query(`CREATE TABLE "messages" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "content" character varying NOT NULL, "role" "public"."messages_role_enum" NOT NULL, "session_id" uuid NOT NULL, CONSTRAINT "PK_18325f38ae6de43878487eff986" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."sessions_status_enum" AS ENUM('OPEN', 'CLOSED', 'HITL')`);
        await queryRunner.query(`CREATE TABLE "sessions" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "external_user_id" character varying NOT NULL, "status" "public"."sessions_status_enum" NOT NULL, "user_id" uuid NOT NULL, "transport_id" uuid NOT NULL, CONSTRAINT "PK_3238ef96f18b355b671619111bc" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."transports_type_enum" AS ENUM('PURE', 'TELEGRAM', 'SLACK', 'LIVECHAT')`);
        await queryRunner.query(`CREATE TABLE "transports" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, "description" character varying NOT NULL, "image" character varying NOT NULL, "type" "public"."transports_type_enum" NOT NULL, CONSTRAINT "PK_f1c7f51afd891fa301da438910e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."invoices_status_enum" AS ENUM('PENDING', 'PAID', 'FAILED', 'CANCELLED', 'REFUNDED')`);
        await queryRunner.query(`CREATE TYPE "public"."invoices_payment_provider_id_enum" AS ENUM('stripe', 'zarinpal')`);
        await queryRunner.query(`CREATE TABLE "invoices" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "credit_id" uuid NOT NULL, "amount" integer NOT NULL, "issued_at" TIMESTAMP NOT NULL, "paid_at" TIMESTAMP NOT NULL, "status" "public"."invoices_status_enum" NOT NULL DEFAULT 'PENDING', "payment_provider_id" "public"."invoices_payment_provider_id_enum" NOT NULL, "payment_method" character varying, "description" character varying, CONSTRAINT "PK_668cef7c22a427fd822cc1be3ce" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."credit_status_enum" AS ENUM('ACTIVE', 'EXPIRED')`);
        await queryRunner.query(`CREATE TABLE "credit" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "user_id" uuid NOT NULL, "available" integer NOT NULL DEFAULT '0', "used" integer NOT NULL DEFAULT '0', "start_date" TIMESTAMP NOT NULL DEFAULT now(), "end_date" TIMESTAMP NOT NULL DEFAULT now(), "status" "public"."credit_status_enum" NOT NULL DEFAULT 'ACTIVE', CONSTRAINT "REL_3544cc02a1d516135f1c265026" UNIQUE ("user_id"), CONSTRAINT "PK_c98add8e192ded18b69c3e345a5" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."knowledge-base_source_type_enum" AS ENUM('LINK', 'FILE')`);
        await queryRunner.query(`CREATE TABLE "knowledge-base" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "user_id" uuid NOT NULL, "name" character varying NOT NULL, "source_type" "public"."knowledge-base_source_type_enum" NOT NULL, "status" character varying NOT NULL DEFAULT 'PENDING', "rde_callback_id" character varying, "s3_id" character varying, CONSTRAINT "PK_9bd3b034e3b5a979079eda6e70f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "name" character varying NOT NULL, "setting" jsonb NOT NULL DEFAULT '{}', "email" character varying NOT NULL, "avatar" character varying, "credit_id" uuid, CONSTRAINT "UQ_97672ac88f789774dd47f7c8be3" UNIQUE ("email"), CONSTRAINT "REL_cbcb0b2b876cd8cc2ce5ea8e2c" UNIQUE ("credit_id"), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "transports_users" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "user_id" uuid NOT NULL, "transport_id" uuid NOT NULL, "setting" jsonb NOT NULL DEFAULT '{}', CONSTRAINT "UQ_transport_user" UNIQUE ("user_id", "transport_id"), CONSTRAINT "PK_23b99193123ffcea1e24362a284" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "prompts" ADD CONSTRAINT "FK_aa88f45bd24d019088a898cf66b" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tools" ADD CONSTRAINT "FK_96158ff69b91b8b52ff6f361174" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_ff71b7760071ed9caba7f02beb4" FOREIGN KEY ("session_id") REFERENCES "sessions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "sessions" ADD CONSTRAINT "FK_085d540d9f418cfbdc7bd55bb19" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "sessions" ADD CONSTRAINT "FK_924c2339e4967d8d3abb3212dfa" FOREIGN KEY ("transport_id") REFERENCES "transports"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "invoices" ADD CONSTRAINT "FK_38fddf505bd78493854c9ecd87d" FOREIGN KEY ("credit_id") REFERENCES "credit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "credit" ADD CONSTRAINT "FK_3544cc02a1d516135f1c265026f" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "knowledge-base" ADD CONSTRAINT "FK_99ebc723f3465ffd17d99fb5393" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "users" ADD CONSTRAINT "FK_cbcb0b2b876cd8cc2ce5ea8e2c3" FOREIGN KEY ("credit_id") REFERENCES "credit"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transports_users" ADD CONSTRAINT "FK_075df65fa2bcfe29ca063d2c757" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "transports_users" ADD CONSTRAINT "FK_b5a3e0692da2c058c39ae190a8e" FOREIGN KEY ("transport_id") REFERENCES "transports"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "transports_users" DROP CONSTRAINT "FK_b5a3e0692da2c058c39ae190a8e"`);
        await queryRunner.query(`ALTER TABLE "transports_users" DROP CONSTRAINT "FK_075df65fa2bcfe29ca063d2c757"`);
        await queryRunner.query(`ALTER TABLE "users" DROP CONSTRAINT "FK_cbcb0b2b876cd8cc2ce5ea8e2c3"`);
        await queryRunner.query(`ALTER TABLE "knowledge-base" DROP CONSTRAINT "FK_99ebc723f3465ffd17d99fb5393"`);
        await queryRunner.query(`ALTER TABLE "credit" DROP CONSTRAINT "FK_3544cc02a1d516135f1c265026f"`);
        await queryRunner.query(`ALTER TABLE "invoices" DROP CONSTRAINT "FK_38fddf505bd78493854c9ecd87d"`);
        await queryRunner.query(`ALTER TABLE "sessions" DROP CONSTRAINT "FK_924c2339e4967d8d3abb3212dfa"`);
        await queryRunner.query(`ALTER TABLE "sessions" DROP CONSTRAINT "FK_085d540d9f418cfbdc7bd55bb19"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_ff71b7760071ed9caba7f02beb4"`);
        await queryRunner.query(`ALTER TABLE "tools" DROP CONSTRAINT "FK_96158ff69b91b8b52ff6f361174"`);
        await queryRunner.query(`ALTER TABLE "prompts" DROP CONSTRAINT "FK_aa88f45bd24d019088a898cf66b"`);
        await queryRunner.query(`DROP TABLE "transports_users"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP TABLE "knowledge-base"`);
        await queryRunner.query(`DROP TYPE "public"."knowledge-base_source_type_enum"`);
        await queryRunner.query(`DROP TABLE "credit"`);
        await queryRunner.query(`DROP TYPE "public"."credit_status_enum"`);
        await queryRunner.query(`DROP TABLE "invoices"`);
        await queryRunner.query(`DROP TYPE "public"."invoices_payment_provider_id_enum"`);
        await queryRunner.query(`DROP TYPE "public"."invoices_status_enum"`);
        await queryRunner.query(`DROP TABLE "transports"`);
        await queryRunner.query(`DROP TYPE "public"."transports_type_enum"`);
        await queryRunner.query(`DROP TABLE "sessions"`);
        await queryRunner.query(`DROP TYPE "public"."sessions_status_enum"`);
        await queryRunner.query(`DROP TABLE "messages"`);
        await queryRunner.query(`DROP TYPE "public"."messages_role_enum"`);
        await queryRunner.query(`DROP TABLE "tools"`);
        await queryRunner.query(`DROP TABLE "prompts"`);
        await queryRunner.query(`DROP TYPE "public"."prompts_type_enum"`);
    }

}
