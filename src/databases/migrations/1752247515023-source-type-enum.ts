import { MigrationInterface, QueryRunner } from "typeorm";

export class SourceTypeEnum1752247515023 implements MigrationInterface {
    name = 'SourceTypeEnum1752247515023'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."knowledge-base_source_type_enum" RENAME TO "knowledge-base_source_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."knowledge-base_source_type_enum" AS ENUM('FILE', 'LINK', 'TEXT', 'QA')`);
        await queryRunner.query(`ALTER TABLE "knowledge-base" ALTER COLUMN "source_type" TYPE "public"."knowledge-base_source_type_enum" USING "source_type"::"text"::"public"."knowledge-base_source_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."knowledge-base_source_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."knowledge-base_source_type_enum_old" AS ENUM('LINK', 'FILE')`);
        await queryRunner.query(`ALTER TABLE "knowledge-base" ALTER COLUMN "source_type" TYPE "public"."knowledge-base_source_type_enum_old" USING "source_type"::"text"::"public"."knowledge-base_source_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."knowledge-base_source_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."knowledge-base_source_type_enum_old" RENAME TO "knowledge-base_source_type_enum"`);
    }

}
