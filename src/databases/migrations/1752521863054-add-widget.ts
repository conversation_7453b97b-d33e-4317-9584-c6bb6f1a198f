import { MigrationInterface, QueryRunner } from "typeorm";

export class AddWidget1752521863054 implements MigrationInterface {
    name = 'AddWidget1752521863054'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."transports_type_enum" RENAME TO "transports_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."transports_type_enum" AS ENUM('PURE', 'TELEGRAM', 'SLACK', 'LIVECHAT', 'DISCORD', 'WIDGET')`);
        await queryRunner.query(`ALTER TABLE "transports" ALTER COLUMN "type" TYPE "public"."transports_type_enum" USING "type"::"text"::"public"."transports_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."transports_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."transports_type_enum_old" AS ENUM('PURE', 'TELEGRAM', 'SLACK', 'LIVECHAT')`);
        await queryRunner.query(`ALTER TABLE "transports" ALTER COLUMN "type" TYPE "public"."transports_type_enum_old" USING "type"::"text"::"public"."transports_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."transports_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."transports_type_enum_old" RENAME TO "transports_type_enum"`);
    }

}
