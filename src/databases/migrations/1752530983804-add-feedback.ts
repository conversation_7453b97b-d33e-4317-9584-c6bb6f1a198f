import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFeedback1752530983804 implements MigrationInterface {
    name = 'AddFeedback1752530983804'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."feedback_feedback_type_enum" AS ENUM('positive', 'negative')`);
        await queryRunner.query(`CREATE TABLE "feedback" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "message_id" uuid NOT NULL, "feedback_type" "public"."feedback_feedback_type_enum" NOT NULL, CONSTRAINT "REL_f05013bed3b4e032e80c6e8d8e" UNIQUE ("message_id"), CONSTRAINT "PK_8389f9e087a57689cd5be8b2b13" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f05013bed3b4e032e80c6e8d8e" ON "feedback" ("message_id") `);
        await queryRunner.query(`ALTER TABLE "feedback" ADD CONSTRAINT "FK_f05013bed3b4e032e80c6e8d8e4" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "feedback" DROP CONSTRAINT "FK_f05013bed3b4e032e80c6e8d8e4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f05013bed3b4e032e80c6e8d8e"`);
        await queryRunner.query(`DROP TABLE "feedback"`);
        await queryRunner.query(`DROP TYPE "public"."feedback_feedback_type_enum"`);
    }

}
