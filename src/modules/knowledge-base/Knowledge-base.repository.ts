import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { KnowledgeBaseEntity } from './entities/knowledge-base.entity';

@Injectable()
export class KnowledgeBaseRepository extends AbstractRepository<KnowledgeBaseEntity> {
  constructor(
    @InjectRepository(KnowledgeBaseEntity)
    repository: Repository<KnowledgeBaseEntity>,
  ) {
    super(repository);
  }
}
