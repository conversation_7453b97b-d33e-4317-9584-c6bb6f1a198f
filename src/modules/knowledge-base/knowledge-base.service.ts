import { BadRequestException, Injectable } from '@nestjs/common';
import { KnowledgeBaseRepository } from './Knowledge-base.repository';
import { SpaceService } from '../../shared/services/space.service';
import { KnowledgeBaseEntity } from './entities/knowledge-base.entity';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { GetManyDtoOptions } from '../../common/abstract.repository';
import { QdrantClient } from '@qdrant/js-client-rest';
import { ApiConfigService } from '../../shared/services/api-config.service';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { createKnowledgeBaseDto } from './dto/create-knowledge-base.dto';
import { updateKnowledgeBaseDto } from './dto/update-knowledge-base.dto';
import { ToolExecutionOptions } from 'ai';
import { RetrieveResponse } from './dto/rde-get.dto';
import { Transactional } from 'typeorm-transactional';
import { randomUUID } from 'crypto';
import { KnowledgeBaseSourceType } from './enums/source-type.enum';
import { KnowledgeBaseStatus } from './enums/knowledge-base-status.enum';
import { QAPair } from './dto/create-qa-knowledge-base.dto copy';

@Injectable()
export class KnowledgeBaseService {
  vectorDb: QdrantClient;
  constructor(
    private readonly repo: KnowledgeBaseRepository,
    private readonly spaceService: SpaceService,
    private readonly apiConfig: ApiConfigService,
    private readonly httpService: HttpService,
  ) {
    this.vectorDb = new QdrantClient({
      url: this.apiConfig.qdrantConfig.url,
      apiKey: this.apiConfig.qdrantConfig.apiKey,
      checkCompatibility: false,
    });
  }

  async sendFileToRDE(userId: string, resourceS3Id: string) {
    const rdeUrl = new URL('/extract/url', this.apiConfig.rdeConfig.url);

    const payload = {
      collection_name: userId,
      url: await this.spaceService.getSignedUrl(resourceS3Id),
    };

    try {
      const res = await firstValueFrom(
        this.httpService.post<{ message: string; id: string; url: string; collection: string }>(
          rdeUrl.toString(),
          payload,
          {
            headers: {
              'Content-Type': 'application/json',
              'api-key': this.apiConfig.rdeConfig.apiKey,
            },
          },
        ),
      );

      return res;
    } catch (error) {
      console.error('RDE call failed:', error);
      throw new Error('Failed to send to RDE');
    }
  }

  async sendLinkToRDE(userId: string, link: string) {
    const rdeUrl = new URL('/extract/url', this.apiConfig.rdeConfig.url);

    const payload = {
      collection_name: userId,
      url: link,
    };

    try {
      const res = await firstValueFrom(
        this.httpService.post<{ message: string; url: string; collection: string; id: string }>(
          rdeUrl.toString(),
          payload,
          {
            headers: {
              'Content-Type': 'application/json',
              'api-key': this.apiConfig.rdeConfig.apiKey,
            },
          },
        ),
      );

      return res;
    } catch (error) {
      console.error('RDE call failed:', error);
      throw new Error('Failed to send to RDE');
    }
  }

  getRetrieverTool(collectionId: string, count: number) {
    const rdeUrl = this.apiConfig.rdeConfig.url;
    const apiKey = this.apiConfig.rdeConfig.apiKey;

    return async function (
      args: {
        query: string;
      },
      _: ToolExecutionOptions,
    ): Promise<string[]> {
      const url = new URL('/retrieve/contents', rdeUrl);

      console.log(`-----------   ${args.query}  -----------`);
      console.log(`-----------   ${collectionId}  -----------`);

      url.search = new URLSearchParams({
        query: args.query,
        collection_name: collectionId,
        top_k: count.toString(),
      }).toString();

      try {
        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'api-key': apiKey,
            Accept: 'application/json',
          },
        });

        if (!response.ok) {
          if (response.status === 400) {
            return [`Error: Bad request - missing 'query' or 'collection_name'`];
          } else if (response.status === 401) {
            return [`Error: Unauthorized - invalid API key`];
          }
          return [`Error: Unexpected status - ${response.status}`];
        }

        const data = (await response.json()) as RetrieveResponse;
        console.log(data.results);
        return data.results;
      } catch (err) {
        return [`Error: ${err instanceof Error ? err.message : String(err)}`];
      }
    };
  }

  async create(args: createKnowledgeBaseDto) {
    const kb = this.repo.create(args);

    return this.repo.save(kb);
  }

  async update(id: string, args: updateKnowledgeBaseDto) {
    const kb = await this.getOne(id);

    kb.assign(args);

    return this.repo.save(kb);
  }

  async getMany<T = KnowledgeBaseEntity>(q: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    const items = await this.repo.getMany<T>(q, opt);

    return items;
  }

  async getOne(id: string) {
    return this.repo.findOrFail({
      where: {
        id,
      },
    });
  }

  async getKnowledgeBase(userId: string) {
    // Ensure collection exists
    await this.vectorDb.getCollection(userId);

    // Fetch total number of vectors for pagination
    const totalResponse = await this.vectorDb.count(userId, {
      exact: true,
    });

    const total = totalResponse.count;

    // Fetch paginated vectors
    const vectors = await this.vectorDb.scroll(userId, {
      with_payload: true,
      with_vector: false, // adjust if you want vectors included
      limit:500
    });

    return {
      items: vectors.points,
      pagination: {
        total,
      },
    };
  }

  async findKnowledgeBaseFromVectorDB(collectionId: string, q: string) {
    const apiKey = this.apiConfig.rdeConfig.apiKey;
    const rdeUrl = this.apiConfig.rdeConfig.url;
    const url = new URL('/retrieve', rdeUrl);

    url.search = new URLSearchParams({
      query: q,
      collection_name: collectionId,
      top_k: '20',
    }).toString();

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'api-key': apiKey,
        Accept: 'application/json',
      },
    });

    if (!response.ok) {
      if (response.status === 400) {
        throw new Error("Bad request: missing 'query' or 'collection_name'");
      } else if (response.status === 401) {
        throw new Error('Unauthorized: invalid API key');
      }
      throw new Error(`Unexpected status: ${response.status}`);
    }

    const data = (await response.json()) as RetrieveResponse;
    console.log(data.results);
    return data.results;
  }

  async deleteFromKnowledgeBase(userId: string, id: string): Promise<void> {
    try {
      // Check if collection exists
      await this.vectorDb.getCollection(userId);
    } catch (err) {
      console.error(`Collection "${userId}" not found or inaccessible`, err);
      throw new Error(`Collection "${userId}" does not exist or is inaccessible`);
    }

    try {
      // Check if the point exists
      const result = await this.vectorDb.retrieve(userId, {
        ids: [id],
      });

      if (result.length === 0) {
        throw new Error(`Vector with id "${id}" does not exist in collection "${userId}"`);
      }

      // Proceed with deletion
      await this.vectorDb.delete(userId, {
        points: [id],
      });
    } catch (err) {
      console.error(`Failed to delete vector "${id}" from collection "${userId}":`, err);
      throw new Error(`Failed to delete vector "${id}": ${err.message}`);
    }
  }

  @Transactional()
  async createFile(userId: string, file: Express.Multer.File) {
    const s3Id = randomUUID();
    const s3Key = `khodkar/${randomUUID()}-${file.originalname}`;

    await this.spaceService.uploadFile(s3Key, file.buffer, file.mimetype);

    const kb = await this.create({
      name: file.originalname,
      s3Id,
      sourceType: KnowledgeBaseSourceType.FILE,
      status: KnowledgeBaseStatus.PENDING,
      userId,
    });

    try {
      const res = await this.sendFileToRDE(kb.userId, s3Key);

      await this.update(kb.id, {
        status: KnowledgeBaseStatus.PROCESSING,
        rdeCallbackId: res.data.id,
      });
    } catch (err) {
      await this.update(kb.id, {
        status: KnowledgeBaseStatus.FAILED,
      });
      throw new BadRequestException('Failed to send file to RDE');
    }
  }

  @Transactional()
  async createLink(userId: string, link: string) {
    const kb = await this.create({
      name: link,
      sourceType: KnowledgeBaseSourceType.LINK,
      status: KnowledgeBaseStatus.PENDING,
      userId,
    });

    const res = await this.sendLinkToRDE(kb.userId, link);
    await this.update(kb.id, {
      status: KnowledgeBaseStatus.PROCESSING,
      rdeCallbackId: res.data.id,
    });
  }

  @Transactional()
  async createFromText(userId: string, content: string) {
    const filename = `${randomUUID()}.md`;
    const s3Key = `khodkar/${randomUUID()}-${filename}`;
    const buffer = Buffer.from(content, 'utf-8');

    await this.spaceService.uploadFile(s3Key, buffer, 'text/markdown');

    const kb = await this.create({
      name: filename,
      s3Id: s3Key,
      sourceType: KnowledgeBaseSourceType.TEXT,
      status: KnowledgeBaseStatus.PENDING,
      userId,
    });

    try {
      const res = await this.sendFileToRDE(kb.userId, s3Key);

      await this.update(kb.id, {
        status: KnowledgeBaseStatus.PROCESSING,
        rdeCallbackId: res.data.id,
      });
    } catch (err) {
      await this.update(kb.id, {
        status: KnowledgeBaseStatus.FAILED,
      });
      throw new BadRequestException('Failed to send plain text to RDE');
    }
  }

  @Transactional()
  async createFromQA(userId: string, qaPairs: QAPair[]) {
    const markdown = qaPairs.map((qa) => `### Q: ${qa.question}\n\nA: ${qa.answer}\n`).join('\n\n');

    const filename = `${randomUUID()}.md`;
    const s3Key = `khodkar/${randomUUID()}-${filename}`;
    const buffer = Buffer.from(markdown, 'utf-8');

    await this.spaceService.uploadFile(s3Key, buffer, 'text/markdown');

    const kb = await this.create({
      name: filename,
      s3Id: s3Key,
      sourceType: KnowledgeBaseSourceType.QA,
      status: KnowledgeBaseStatus.PENDING,
      userId,
    });

    try {
      const res = await this.sendFileToRDE(kb.userId, s3Key);

      await this.update(kb.id, {
        status: KnowledgeBaseStatus.PROCESSING,
        rdeCallbackId: res.data.id,
      });
    } catch (err) {
      await this.update(kb.id, {
        status: KnowledgeBaseStatus.FAILED,
      });
      throw new BadRequestException('Failed to send Q&A to RDE');
    }
  }
}
