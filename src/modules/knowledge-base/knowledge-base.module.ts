import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KnowledgeBaseEntity } from './entities/knowledge-base.entity';
import { KnowledgeBaseRepository } from './Knowledge-base.repository';
import { KnowledgeBaseController } from './knowledge-base.controller';
import { BillingModule } from '../billing/billing.module';
import { KnowledgeBaseService } from './knowledge-base.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [TypeOrmModule.forFeature([KnowledgeBaseEntity]), BillingModule, HttpModule],
  providers: [KnowledgeBaseRepository, KnowledgeBaseService],
  controllers: [KnowledgeBaseController],
  exports: [KnowledgeBaseService],
})
export class KnowledgeBaseModule {}
