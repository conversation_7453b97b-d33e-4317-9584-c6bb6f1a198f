import { AbstractDto } from '../../../common/dto/abstract.dto';
import { EnumField, StringField, StringFieldOptional, UUIDField } from '../../../decorators';
import { UserDto } from '../../../modules/user/dtos/user.dto';
import { KnowledgeBaseSourceType } from '../enums/source-type.enum';
import { KnowledgeBaseStatus } from '../enums/knowledge-base-status.enum';
import { KnowledgeBaseEntity } from '../entities/knowledge-base.entity';

export class KnowledgeBaseDto extends AbstractDto {
  @UUIDField()
  userId: string;

  @StringField()
  name: string;

  @EnumField(() => KnowledgeBaseSourceType)
  sourceType: KnowledgeBaseSourceType;

  @EnumField(() => KnowledgeBaseStatus)
  status: KnowledgeBaseStatus;

  @StringFieldOptional()
  rdeCallbackId?: string;

  @StringFieldOptional()
  s3Id?: string;

  user: UserDto;

  constructor(e: KnowledgeBaseEntity) {
    super(e);

    this.name = e.name;
    this.sourceType = e.sourceType;
    this.status = e.status;
    this.rdeCallbackId = e.rdeCallbackId;
    this.userId = e.userId;
    this.s3Id = e.s3Id;
    this.user = e.user?.toDto()

  }
}
