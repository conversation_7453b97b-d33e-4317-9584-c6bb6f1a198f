import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsObject, ValidateNested } from 'class-validator';
import { StringField } from '../../../decorators';

export class QAPair {
  @StringField()
  question: string;

  @StringField()
  answer: string;
}

export class CreateQAKnowledgeBaseDto {
  @ApiProperty({
    type: () => QAPair,
  })
  @IsArray()
  @IsObject()
  @ValidateNested({ each: true })
  @Type(() => QAPair)
  QaPairs: QAPair[];
}
