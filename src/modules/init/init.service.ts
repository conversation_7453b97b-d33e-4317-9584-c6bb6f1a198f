import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { promptType } from '../prompt/enums/prompt-type.enum';
import { TransportType } from '../transports/enums/transport-type.enum';
import { PromptRepository } from '../prompt/prompt.repository';
import { TransportRepository } from '../transports/transport.repository';
import { In } from 'typeorm';

@Injectable()
export class InitService implements OnApplicationBootstrap {
  private readonly logger = new Logger(InitService.name);

  constructor(
    private readonly promptRepo: PromptRepository,
    private readonly transportRepo: TransportRepository,
  ) {}

  async onApplicationBootstrap(): Promise<void> {
    this.logger.log('Running InitService...');
    await this.seedPrompts();
    await this.seedTransports();
  }

  private async seedPrompts() {
    const count = await this.promptRepo.count();
    if (count === 0) {
      this.logger.log('Seeding default prompt...');
      await this.promptRepo.save({
        content: '',
        setting: {
          outputControl: {
            frequencyPenalty: 0,
            maxTokens: 1600,
            presencePenalty: 1,
          },
          sampling: {
            temp: 0.4,
            topK: 5,
            topP: 0.3,
          },
        },
        type: promptType.DEFAULT_CUSTOMER_SUPPORT,
      });
    } else {
      this.logger.log('Prompt already seeded');
    }
  }

  private async seedTransports() {
    const count = await this.transportRepo.count({
      where: {
        type: In(Object.values(TransportType)),
      },
    });

    if (count === 0) {
      this.logger.log('Seeding transport...');
      await this.transportRepo.save({
        name: 'Pure API',
        description: '',
        image: 'https://example.com/transport.png',
        type: TransportType.PURE,
      });
    } else {
      this.logger.log('Transports already seeded');
    }
  }
}
