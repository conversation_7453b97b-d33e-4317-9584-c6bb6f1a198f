import { Injectable, NotFoundException } from '@nestjs/common';
import { SessionRepository } from './sessions.repository';
import { MessageService } from '../messages/message.service';
import { LlmService } from '../llm/llm.service';
import { MessageRole } from '../messages/enums/role.enum';
import { PromptService } from '../prompt/prompt.service';
import { CoreMessage, ToolExecutionOptions } from 'ai';
import { SessionStatusType } from './enums/status.enum';
import { ToolService } from '../tools/tools.service';
import { BillingService } from '../billing/billing.service';
import { LessThan, MoreThan, Not } from 'typeorm';
import { z } from 'zod';
import { KnowledgeBaseService } from '../knowledge-base/knowledge-base.service';
import { SessionsEntity } from './entities/sessions.entity';
import { GetManyDto } from 'src/common/dto/get-many.dto';
import { GetManyDtoOptions } from 'src/common/abstract.repository';
import { UserService } from '../user/user.service';
import nunjucks from 'nunjucks';
import { Transactional } from 'typeorm-transactional';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class SessionService {
  constructor(
    private readonly repo: SessionRepository,
    private readonly messageService: MessageService,
    private readonly llmService: LlmService,
    private readonly promptService: PromptService,
    private readonly toolService: ToolService,
    private readonly billing: BillingService,
    private readonly kbService: KnowledgeBaseService,
    private readonly userService: UserService,
    private eventEmitter: EventEmitter2,
  ) {
    const env = nunjucks.configure({});

    env.addFilter('keys', (obj) => {
      return Object.keys(obj || {});
    });
  }

  @Transactional()
  async initSession(userId: string, externalUserId: string, transportId: string) {
    await this.billing.ensureSufficientCreditOrThrow(userId, 1);
    await this.billing.consumeCredit(userId, 1);

    const session = this.repo.create({
      userId,
      externalUserId,
      transportId,
      status: SessionStatusType.OPEN,
    });

    return this.repo.save(session);
  }

  @Transactional()
  async continueSession(sessionId: string, content: string) {
    const session = await this.repo.findOrFail({
      where: {
        id: sessionId,
        status: SessionStatusType.OPEN,
      },
    });

    await this.messageService.create(content, MessageRole.USER, session.id);

    const [msgs, promptData, tools, user] = await Promise.all([
      this.messageService.retrieveSessionHistory(session.id),
      this.promptService.findOneCostumerSupportByUserId(session.userId),
      this.toolService.getAllAsMCPFunctions(session.userId),
      this.userService.findById(session.userId),
    ]);

    tools['SearchRAG'] = {
      description:
        'Performs Retrieval‑Augmented Generation by semantically querying your internal knowledge base, returning the most relevant documents or snippets to support grounded, accurate responses rooted in your proprietary data.',
      parameters: z.object({
        query: z
          .string()
          .describe(
            'Parameter: query (string) — A natural‑language question or query to retrieve context or documents from the knowledge base. Returns up to 5 top‑ranked snippets to help generate informed, citation‑anchored answers.',
          ),
      }),
      execute: this.kbService.getRetrieverTool(session.userId, 5),
    };

    const systemPrompt = nunjucks.renderString(promptData.content, {
      agentName: user.setting.variables.agentName,
      businessName: user.setting.variables.businessName,
      businessType: user.setting.variables.businessType,
      businessDescription: user.setting.variables.businessDescription,
      tools,
    });

    const { text, usage, toolCallCount } = await this.llmService.generateText(
      msgs.map((m) => {
        return {
          content: m.content,
          role: m.role,
        } as CoreMessage;
      }),
      systemPrompt,
      tools,
      promptData.setting,
      {
        sessionId: session.id,
        userId: session.userId,
      },
    );

    let response = text;

    if (response.includes('[SESSION_CLOSED]')) {
      await this.closeSession(session.id);
      response = text.replaceAll('[SESSION_CLOSED]', '');
      this.eventEmitter.emit('session.closed', { sessionId, externalUserId: session.externalUserId });
    }

    const tokensUsed = usage.promptTokens + usage.completionTokens;

    // Base: 1 credit per 1000 tokens (ceil to always round up)
    let creditsToConsume = Math.ceil(tokensUsed / 1000);

    // Light penalty: add 1 extra credit for every 5 tool calls
    if (toolCallCount && toolCallCount > 0) {
      creditsToConsume += Math.floor(toolCallCount / 2);
    }

    // Ensure minimum cost is at least 1
    creditsToConsume = Math.max(1, creditsToConsume);

    await this.billing.ensureSufficientCreditOrThrow(session.userId, creditsToConsume);
    await this.billing.consumeCredit(session.userId, creditsToConsume);

    await this.messageService.create(response, MessageRole.ASSISTANT, session.id);

    return this.messageService.retrieveSessionHistory(session.id);
  }

  async findOne(id: string) {
    const session = await this.repo.findOne({ where: { id: id } });

    if (!session) {
      throw new NotFoundException('Session not found');
    }

    return session;
  }

  async closeSession(id: string) {
    const session = await this.repo.findOrFail({
      where: {
        id,
        status: Not(SessionStatusType.CLOSED),
      },
    });

    session.status = SessionStatusType.CLOSED;

    return this.repo.save(session);
  }

  @Transactional()
  async autoCloseInactiveSessions(timeoutMinutes = 30) {
    const cutoff = new Date(Date.now() - timeoutMinutes * 60 * 1000);

    const staleSessions = await this.repo.findAll({
      where: {
        status: SessionStatusType.OPEN,
        updatedAt: LessThan(cutoff),
      },
    });

    for (const session of staleSessions) {
      await this.closeSession(session.id);
      await this.eventEmitter.emit('session.closed', { sessionId: session.id, externalUserId: session.externalUserId });
    }

    return staleSessions.length;
  }

  async findActiveSession(userId: string, transportId: string, externalUserId: string, timeoutMinutes = 30) {
    const cutoff = new Date(Date.now() - timeoutMinutes * 60 * 1000);

    return this.repo.findOne({
      where: {
        userId,
        externalUserId,
        transportId,
        status: SessionStatusType.OPEN,
        updatedAt: MoreThan(cutoff),
      },
    });
  }

  async getMany<T = SessionsEntity>(q: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    const items = await this.repo.getMany<T>(q, opt);

    return items;
  }
}
