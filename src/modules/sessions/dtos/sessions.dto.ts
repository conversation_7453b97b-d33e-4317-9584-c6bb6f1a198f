import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, UUI<PERSON>ield } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { UserDto } from '../../user/dtos/user.dto';
import { SessionsEntity } from '../entities/sessions.entity';
import { SessionStatusType } from '../enums/status.enum';
import { TransportDto } from '../../transports/dtos/transport.dto';
import { MessageDto } from 'src/modules/messages/dtos/messages.dto';

export class SessionsDto extends AbstractDto {
  @StringField()
  externalUserId: string;

  @EnumField(() => SessionStatusType)
  status: SessionStatusType | keyof typeof SessionStatusType;

  user?: UserDto;

  @UUIDField()
  userId?: string;

  transport?: TransportDto;

  @UUIDField()
  transportId?: string;

  messages?: MessageDto[];

  constructor(e: SessionsEntity) {
    super(e);

    this.externalUserId = e.externalUserId;
    this.status = e.status;
    this.user = e.user?.toDto();
    this.userId = e.userId;
    this.transportId = e.transportId;
    this.transport = e.transport?.toDto();
    this.messages = e.messages?.toDtos()
  }
}
