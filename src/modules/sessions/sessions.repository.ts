import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { SessionsEntity } from './entities/sessions.entity';

@Injectable()
export class SessionRepository extends AbstractRepository<SessionsEntity> {
  constructor(
    @InjectRepository(SessionsEntity)
    repository: Repository<SessionsEntity>,
  ) {
    super(repository);
  }
}
