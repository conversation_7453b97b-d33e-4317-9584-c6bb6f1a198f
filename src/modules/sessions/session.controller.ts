import { Controller, Delete, Get, NotFoundException, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { GuardUser } from '../../decorators/guard-user.decorator';
import { ApiGetManyDto } from '../../decorators';
import { replaceGetManyFilter } from '../../common/utils';
import { SessionService } from './session.service';
import { SessionsDto } from './dtos/sessions.dto';
import { SessionsEntity } from './entities/sessions.entity';

@Controller('sessions')
@ApiTags('Sessions')
export class SessionController {
  constructor(private readonly sessionService: SessionService) {}

  @Get()
  @ApiOperation({
    summary: 'Retrieve a list of user sessions',
    description: 'This endpoint returns a paginated list of sessions associated with the authenticated user.',
  })
  @ApiGetManyDto({ type: SessionsDto, description: 'Paginated list of Session records' })
  @GuardUser()
  async getMany(@AuthUser() user: User, @Query() q: GetManyDto<SessionsEntity>) {
    q.filter = replaceGetManyFilter(q.filter, 'userId', `userId:eq:${user.externalId!}`);

    const sessions = await this.sessionService.getMany(q, {
      toDto: true,
      joinables: ['messages', 'transport'],
    });
    return sessions;
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Retrieve a specific session by ID',
    description: 'This endpoint returns detailed information about a specific session identified by its ID.',
  })
  @GuardUser()
  async getOne(@AuthUser() user: User, @Param('id') id: string) {
    const { data } = await this.sessionService.getMany(
      { filter: [`id:eq:${id}`, `userId:eq:${user.externalId!}`], join: ['messages', 'transport'] },
      {
        joinables: ['messages', 'transport'],
        toDto: true,
      },
    );

    if (data.length == 0) {
      throw new NotFoundException('session not found');
    }

    return data[0];
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Close a specific session by ID',
  })
  @GuardUser()
  async closeSession(@AuthUser() user: User, @Param('id') id: string) {
    const { data } = await this.sessionService.getMany(
      { filter: [`userId:eq:${user.externalId!}`, `id:eq:${id}`], page: 1, limit: 1 },
      {},
    );

    if (data.length == 0) {
      throw new NotFoundException('session not found');
    }

    await this.sessionService.closeSession(data[0]!.id);
  }
}
