import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToMany, ManyToOne, OneToMany, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { SessionsDto } from '../dtos/sessions.dto';
import { SessionStatusType } from '../enums/status.enum';
import { TransportEntity } from '../../transports/entities/transport.entity';
import { MessageEntity } from '../../messages/entities/messages.entity';
import { UUID } from 'crypto';

@Entity({ name: 'sessions' })
export class SessionsEntity extends AbstractEntity<SessionsDto> {
  dtoClass = SessionsDto;

  @Column({ nullable: false })
  externalUserId: string;

  @Column({ type: "enum", enum: SessionStatusType })
  status: SessionStatusType | keyof typeof SessionStatusType;

  @ManyToOne(() => UserEntity, (user) => user.sessions)
  @JoinColumn({name:"user_id"})
  user?: UserEntity;

  @Column()
  userId: string

  @ManyToOne(() => TransportEntity, (transport) => transport.sessions)
  @JoinColumn({name:"transport_id"})
  transport?: TransportEntity;

  @Column()
  transportId?: string

  @OneToMany(() => MessageEntity, (message) => message.session)
  messages?: MessageEntity[];

  constructor(item?: Partial<SessionsEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<SessionsEntity>): void {
    super.assign(item);

    this.externalUserId = item.externalUserId ?? this.externalUserId;
    this.status = item.status ?? this.status;
  }
}
