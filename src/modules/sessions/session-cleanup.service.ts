import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SessionService } from './session.service';

@Injectable()
export class SessionCleanupService {
  constructor(private readonly sessionService: SessionService) {}

  @Cron(CronExpression.EVERY_5_MINUTES)
  async handleSessionCleanup() {
    const closedCount = await this.sessionService.autoCloseInactiveSessions();
    if (closedCount > 0) {
      console.log(`Auto-closed ${closedCount} inactive sessions.`);
    }
  }
}
