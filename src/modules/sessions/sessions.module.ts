import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SessionsEntity } from './entities/sessions.entity';
import { SessionRepository } from './sessions.repository';
import { SessionService } from './session.service';
import { MessagesModule } from '../messages/messages.module';
import { LlmModule } from '../llm/llm.module';
import { PromptModule } from '../prompt/prompt.module';
import { ToolsModule } from '../tools/tools.module';
import { BillingModule } from '../billing/billing.module';
import { SessionCleanupService } from './session-cleanup.service';
import { KnowledgeBaseModule } from '../knowledge-base/knowledge-base.module';
import { SessionController } from './session.controller';
import { UserModule } from '../user/user.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([SessionsEntity]),
    MessagesModule,
    LlmModule,
    PromptModule,
    ToolsModule,
    BillingModule,
    KnowledgeBaseModule,
    UserModule,
  ],
  providers: [SessionRepository, SessionService, SessionCleanupService],
  controllers: [SessionController],
  exports: [SessionService],
})
export class SessionsModule {}
