import { <PERSON>, Get, HttpStatus } from '@nestjs/common';
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger';
import {
  HealthCheck,
  HealthCheckService,
  TypeOrmHealthIndicator,
  DiskHealthIndicator,
  MemoryHealthIndicator,
  HealthCheckResult,
  HttpHealthIndicator,
} from '@nestjs/terminus';
import { QdrantClient } from '@qdrant/js-client-rest';
import { ApiConfigService } from '../../shared/services/api-config.service';
import { SpaceService } from '../../shared/services/space.service';
import { Public } from '../../decorators/public.decorator';

@Controller('health')
@ApiTags('health')
export default class HealthController {
  private readonly startTime = Date.now();

  constructor(
    private readonly health: HealthCheckService,
    private readonly typeOrm: TypeOrmHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly memory: MemoryHealthIndicator,
    private readonly http: HttpHealthIndicator,
    private readonly apiConfig: ApiConfigService,
    private readonly spaceService: SpaceService,
  ) {}

  @Get()
  @HealthCheck()
  @Public()
  async check() {
    const result: HealthCheckResult = await this.health.check([
      () => this.typeOrm.pingCheck('database', { timeout: 3000 }),
      () => this.disk.checkStorage('disk', { thresholdPercent: 0.8, path: '/' }),
      () => this.memory.checkHeap('memory_heap', 800 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 1000 * 1024 * 1024),
      async () => this.http.pingCheck('rag-data-extractor', new URL('/docs', this.apiConfig.rdeConfig.url).toString()),
      async () => {
        try {
          const qdrantClient = new QdrantClient({
            url: this.apiConfig.qdrantConfig.url,
            apiKey: this.apiConfig.qdrantConfig.apiKey,
            checkCompatibility: false,
          });
          await qdrantClient.getCollections();
          return { qdrant: { status: 'up' } };
        } catch (err) {
          throw new Error(`Qdrant health check failed: ${err.message}`);
        }
      },
      () =>
        this.http.pingCheck('openrouter_credits', new URL('/credits', this.apiConfig.openAiConfig.url).toString(), {
          headers: {
            Authorization: `Bearer ${this.apiConfig.openAiConfig.apiKey}`,
          },
        }),
      async () => {
        try {
          await this.spaceService.getListBucket();
          return { s3: { status: 'up' } };
        } catch (err) {
          throw new Error(`S3 health check failed: ${err.message}`);
        }
      },
    ]);

    // Determine overall status
    const isHealthy = result.status === 'ok';

    return {
      status: isHealthy ? 'ok' : 'error',
      details: result.info || result.error, // includes individual checks results
      uptime: this.getFormattedUptime(),
    };
  }

  private getFormattedUptime() {
    const elapsedMs = Date.now() - this.startTime;
    const totalSeconds = Math.floor(elapsedMs / 1000);
    const days = Math.floor(totalSeconds / 86400);
    const hours = Math.floor((totalSeconds % 86400) / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return { days, hours, minutes, seconds };
  }
}
