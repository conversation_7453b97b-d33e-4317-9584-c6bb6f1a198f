import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import * as nodemailer from 'nodemailer';
import path from 'path';
import * as handlebars from 'handlebars';
import { ApiConfigService } from '../../shared/services/api-config.service';
import { EmailTemplateContext } from './interfaces/notification-events.interface';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly apiConfig: ApiConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.apiConfig.mailConfig.host,
      port: this.apiConfig.mailConfig.port,
      secure: this.apiConfig.mailConfig.secure,
      auth: {
        user: this.apiConfig.mailConfig.user,
        pass: this.apiConfig.mailConfig.pass,
      },
    });
  }

  /**
   * Compile handlebars template with context
   */
  private compileTemplate(templateName: string, context: Record<string, any>): string {
    const filePath = path.join(__dirname, 'templates', `${templateName}.hbs`);
    const source = readFileSync(filePath, 'utf-8').toString();
    const template = handlebars.compile(source);
    return template(context);
  }

  /**
   * Send email using template
   * @param to Recipient email address
   * @param subject Email subject
   * @param templateName Template name (without .hbs extension)
   * @param context Template context variables
   */
  async sendTemplateEmail(
    to: string,
    subject: string,
    templateName: string,
    context: Record<string, any>
  ): Promise<string> {
    try {
      const html = this.compileTemplate(templateName, context);

      const info = await this.transporter.sendMail({
        from: `"${this.apiConfig.mailConfig.senderName}" <${this.apiConfig.mailConfig.user}>`,
        to,
        subject,
        html,
      });

      this.logger.log(`Email sent successfully to ${to} with subject: ${subject}`);
      return info.messageId;
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send notification email with typed context
   */
  async sendNotificationEmail<T extends EmailTemplateContext>(
    to: string,
    subject: string,
    templateName: string,
    context: T
  ): Promise<string> {
    return this.sendTemplateEmail(to, subject, templateName, context);
  }
}
