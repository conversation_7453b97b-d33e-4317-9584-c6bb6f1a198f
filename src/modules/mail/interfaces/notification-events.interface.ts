import { InvoiceEntity } from '../../billing/entities/invoice.entity';
import { CreditEntity } from '../../billing/entities/credit.entity';
import { UserEntity } from '../../user/entities/user.entity';

/**
 * Base interface for all notification events
 */
export interface BaseNotificationEvent {
  timestamp: Date;
  eventId: string;
}

/**
 * Event emitted when a user registers
 */
export interface UserRegisteredEvent extends BaseNotificationEvent {
  user: UserEntity;
  verificationToken?: string;
}

/**
 * Event emitted when user credits fall below threshold
 */
export interface CreditLowEvent extends BaseNotificationEvent {
  userId: string;
  user: UserEntity;
  credit: CreditEntity;
  remainingCredits: number;
  totalCredits: number;
  usagePercentage: number;
  threshold: number;
}

/**
 * Event emitted when an invoice is paid
 */
export interface InvoicePaidEvent extends BaseNotificationEvent {
  invoice: InvoiceEntity;
  user: UserEntity;
  creditsPurchased: number;
}

/**
 * Union type of all notification events
 */
export type NotificationEvent =
  | UserRegisteredEvent
  | CreditLowEvent
  | InvoicePaidEvent

/**
 * Event name constants for type safety
 */
export const NOTIFICATION_EVENTS = {
  USER_REGISTERED: 'user.registered',
  CREDIT_LOW: 'credit.low',
  INVOICE_PAID: 'invoice.paid',
} as const;

/**
 * Type for notification event names
 */
export type NotificationEventName = typeof NOTIFICATION_EVENTS[keyof typeof NOTIFICATION_EVENTS];

/**
 * Context data for email templates
 */
export interface EmailTemplateContext {
  user_name: string;
  user_email: string;
  [key: string]: string | number | boolean;
}

/**
 * Low credit email template context
 */
export interface LowCreditTemplateContext extends EmailTemplateContext {
  remaining_credits: string;
  monthly_usage: string;
  support_url: string;
  current_year: string;
}

/**
 * Invoice paid email template context
 */
export interface InvoicePaidTemplateContext extends EmailTemplateContext {
  invoice_number: string;
  payment_date: string;
  credits_purchased: string;
  payment_method: string;
  total_amount: string;
  new_balance: string;
  dashboard_url: string;
  download_invoice_url: string;
  support_url: string;
  current_year: string;
}
