import { Module } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { NotificationListener } from './notification-listener.service';
import { UserModule } from '../user/user.module';
import { BillingModule } from '../billing/billing.module';

@Module({
  imports: [UserModule, BillingModule],
  providers: [
    NotificationService,
    NotificationListener,
  ],
  exports: [
    NotificationService,
  ],
})
export class NotificationModule {}
