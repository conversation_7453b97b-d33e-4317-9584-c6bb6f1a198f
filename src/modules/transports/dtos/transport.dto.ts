import { AbstractDto } from '../../../common/dto/abstract.dto';
import { UserDto } from '../../user/dtos/user.dto';
import { TransportEntity } from '../entities/transport.entity';
import { SessionsDto } from '../../sessions/dtos/sessions.dto';
import { EnumField, StringField } from '../../../decorators';
import { TransportType } from '../enums/transport-type.enum';

export class TransportDto extends AbstractDto {
  @StringField()
  name: string;

  @StringField()
  description: string;

  @StringField()
  image: string;

  @EnumField(() => TransportType)
  type: TransportType | keyof typeof TransportType;

  users?: UserDto[];

  sessions?: SessionsDto[];

  constructor(e: TransportEntity) {
        super(e);

        this.name = e.name
        this.description = e.description
        this.image = e.image
        this.type = e.type
        this.sessions = e.sessions?.toDtos()
  }
}
