import { ClassFieldOptional, StringFieldOptional } from '../../../decorators';

export class TelegramConfig {
  @StringFieldOptional()
  token?: string;
}

export class PureApiConfig {
  @StringFieldOptional()
  apiKey?: string;
}

export class WidgetConfig {
  @StringFieldOptional()
  publishableKey?: string;
}

export class DiscordConfig {
  @StringFieldOptional()
  token?: string;
}

export class TransportConfigDto {
  @ClassFieldOptional(() => TelegramConfig)
  telegram?: TelegramConfig;

  @ClassFieldOptional(() => PureApiConfig)
  pure?: PureApiConfig;

  @ClassFieldOptional(() => DiscordConfig)
  discord?: DiscordConfig;

  @ClassFieldOptional(() => WidgetConfig)
  widget?: WidgetConfig;
}
