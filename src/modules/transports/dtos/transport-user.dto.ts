import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Class<PERSON>ield, UUIDField } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { UserDto } from '../../user/dtos/user.dto';
import { TransportUserEntity } from '../entities/transport-user.entity';
import { TransportDto } from './transport.dto';
import { TransportConfigDto } from './transport-setting.dto';

export class TransportUserDto extends AbstractDto {
  @UUIDField()
  userId: string;

  user?: UserDto;

  @UUIDField()
  transportId: string;

  transport?: TransportDto;

  // @ClassField(() => TransportConfigDto)
  setting: TransportConfigDto;

  constructor(e: TransportUserEntity) {
    super(e);

    this.user = e.user?.toDto();
    this.transport = e.transport?.toDto();
    this.userId = e.userId;
    this.transportId = e.transportId;
    this.setting = {
      pure: {
        apiKey: e.setting.pure?.apiKey ?? undefined,
      },
      discord: {
        token: e.setting.discord?.token ?? undefined,
      },
      telegram: {
        token: e.setting.telegram?.token ?? undefined,
      },
      widget:{
        publishableKey: e.setting.widget?.publishableKey ?? undefined
      }
    };
  }
}
