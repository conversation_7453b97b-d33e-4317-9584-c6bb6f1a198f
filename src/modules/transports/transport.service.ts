import { BadRequestException, ConflictException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { randomBytes } from 'crypto';
import { TransportUserRepository } from './transport-user.repository';
import { TransportRepository } from './transport.repository';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { TransportEntity } from './entities/transport.entity';
import { GetManyDtoOptions } from '../../common/abstract.repository';
import { TransportUserEntity } from './entities/transport-user.entity';
import { TransportType } from './enums/transport-type.enum';
import { Raw } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ApiConfigService } from '../../shared/services/api-config.service';
import { AxiosError } from 'axios';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class TransportService {
  constructor(
    private readonly transportUserRepo: TransportUserRepository,
    private readonly transportRepo: TransportRepository,
    private readonly httpService: HttpService,
    private readonly apiConfig: ApiConfigService,
  ) { }

  async generateAPIKey() {
    const key = `sk-kh-${randomBytes(32).toString('base64url')}`;
    return key;
  }

  async generatePublishableKey() {
    const key = `pk-kh-${randomBytes(32).toString('base64url')}`;
    return key;
  }

  async getOneTransport(id: string) {
    const transport = await this.transportRepo.findById(id);
    if (!transport) {
      throw new NotFoundException('User not found');
    }
    return transport;
  }

  async getManyTransport<T = TransportEntity>(args: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    const items = await this.transportRepo.getMany<T>(args, opt);

    return items;
  }

  async getOneUserTransport(id: string) {
    const transport = await this.transportUserRepo.findById(id);
    if (!transport) {
      throw new NotFoundException('User transport not found');
    }
    return transport;
  }

  async getManyUserTransport<T = TransportUserEntity>(args: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    const items = await this.transportUserRepo.getMany<T>(args, opt);

    return items;
  }

  async revoke(transportUserId: string) {
    await this.transportUserRepo.deleteById(transportUserId);
  }

  @Transactional()
  async createPureApiTransportUser(userId: string, apiKey: string) {
    const transport = await this.transportRepo.findOrFail({
      where: {
        type: TransportType.PURE,
      },
    });

    const isExist = await this.transportUserRepo.findOne({
      where: {
        userId,
        transportId: transport.id,
      },
    });

    if (isExist) {
      throw new ConflictException('user transport is exist');
    }

    const userTransport = this.transportUserRepo.create();
    userTransport.assign({
      userId,
      transportId: transport.id,
      setting: {
        pure: { apiKey: apiKey },
      },
    });

    return this.transportUserRepo.save(userTransport);
  }

  @Transactional()
  async createWidgetTransportUser(userId: string, publishableKey: string) {
    const transport = await this.transportRepo.findOrFail({
      where: {
        type: TransportType.WIDGET,
      },
    });

    const isExist = await this.transportUserRepo.findOne({
      where: {
        userId,
        transportId: transport.id,
      },
    });

    if (isExist) {
      throw new ConflictException('user transport is exist');
    }

    const userTransport = this.transportUserRepo.create();
    userTransport.assign({
      userId,
      transportId: transport.id,
      setting: {
        widget: {
          publishableKey: publishableKey,
        },
      },
    });

    return this.transportUserRepo.save(userTransport);
  }

  @Transactional()
  async createTelegramTransportUser(userId: string, telegramToken: string) {
    const transport = await this.transportRepo.findOrFail({
      where: { type: TransportType.TELEGRAM },
    });

    const isExist = await this.transportUserRepo.findOne({
      where: { userId, transportId: transport.id },
    });

    if (isExist) {
      throw new ConflictException('User already has a Telegram transport');
    }

    const userTransport = this.transportUserRepo.create();
    userTransport.assign({
      userId,
      transportId: transport.id,
      setting: {
        telegram: { token: telegramToken },
      },
    });

    const webhookUrl = this.apiConfig.transportConfig.telegram.webhookUrl;

    try {
      await firstValueFrom(
        this.httpService.post(`https://api.telegram.org/bot${telegramToken}/setWebhook`, {
          url: webhookUrl,
          secret_token: userId,
        }),
      );
    } catch (error) {
      if (error instanceof AxiosError && error.response?.data) {
        const telegramError = error.response.data;

        if (telegramError?.description) {
          throw new BadRequestException(`Telegram error: ${telegramError.description}`);
        }
      }

      throw new InternalServerErrorException('Failed to set Telegram webhook');
    }

    return this.transportUserRepo.save(userTransport);
  }

  async getOneTransportUserByApiKey(hash: string) {
    return this.transportUserRepo.findOrFail({
      where: {
        setting: Raw((alias) => `${alias} @> '{"pure": {"apiKey": "${hash}"}}'`),
      },
    });
  }

  async getOneTransportUserByPublishableKey(hash: string) {
    return this.transportUserRepo.findOrFail({
      where: {
        setting: Raw((alias) => `${alias} @> '{"widget": {"publishableKey": "${hash}"}}'`),
      },
    });
  }
}
