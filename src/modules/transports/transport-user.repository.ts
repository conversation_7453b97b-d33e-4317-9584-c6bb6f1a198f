import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { TransportUserEntity } from './entities/transport-user.entity';

@Injectable()
export class TransportUserRepository extends AbstractRepository<TransportUserEntity> {
  constructor(
    @InjectRepository(TransportUserEntity)
    repository: Repository<TransportUserEntity>,
  ) {
    super(repository);
  }
}
