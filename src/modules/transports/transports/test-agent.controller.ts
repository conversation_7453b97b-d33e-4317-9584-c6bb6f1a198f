import { Body, Controller, Delete, Get, NotFoundException, Param, Patch, Post, Query } from '@nestjs/common';
import { SessionService } from '../../sessions/session.service';
import { GuardUser } from '../../../decorators/guard-user.decorator';
import { AuthUser } from '../../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { TransportService } from '../transport.service';
import { TransportType } from '../enums/transport-type.enum';
import { ContinueSessionDto } from '../../sessions/dtos/continue-session.dto';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { ApiGetManyDto } from '../../../decorators';
import { SessionsDto } from '../../../modules/sessions/dtos/sessions.dto';
import { GetManyDto } from '../../../common/dto/get-many.dto';
import { SessionsEntity } from '../../../modules/sessions/entities/sessions.entity';
import { replaceGetManyFilter } from '../../../common/utils';

@Controller('transport/test-agent')
@ApiTags('Transport/test-agent')
export class AgentTestController {
  constructor(
    private readonly sessionService: SessionService,
    private readonly transportService: TransportService,
  ) {}

  @Post()
  @GuardUser()
  async init(@AuthUser() user: User) {
    const { data: transport } = await this.transportService.getManyTransport(
      { filter: [`type:eq:${TransportType.PURE}`], limit: 1, page: 1 },
      {},
    );

    if (transport.length == 0) {
      throw new NotFoundException('transport not found');
    }

    const a = await this.sessionService.initSession(user.externalId!, 'AgentTest', transport[0]!.id);

    return a.toDto();
  }

  @Delete(':id')
  @GuardUser()
  async close(@AuthUser() user: User, @Param('id') id: string) {
    const { data: session } = await this.sessionService.getMany(
      { filter: [`userId:eq:${user.externalId!}`, `id:eq:${id}`], limit: 1, page: 1 },
      {},
    );

    if (session.length == 0) {
      throw new NotFoundException('session not found');
    }

    await this.sessionService.closeSession(id);
  }

  @Patch(':id')
  @GuardUser()
  async continue(@AuthUser() user: User, @Param('id') id: string, @Body() { content }: ContinueSessionDto) {
    const { data: session } = await this.sessionService.getMany(
      { filter: [`userId:eq:${user.externalId!}`, `id:eq:${id}`], limit: 1, page: 1 },
      {},
    );

    if (session.length == 0) {
      throw new NotFoundException('session not found');
    }

    const msg = await this.sessionService.continueSession(session[0]!.id, content);

    return msg.toDtos();
  }

  @Get()
  @ApiOperation({
    summary: 'Retrieve a list of user agent test sessions',
    description: 'This endpoint returns a paginated list of sessions associated with the authenticated user.',
  })
  @ApiGetManyDto({ type: SessionsDto, description: 'Paginated list of Session records' })
  @GuardUser()
  async getManyAgentTest(@AuthUser() user: User, @Query() q: GetManyDto<SessionsEntity>) {
    q.filter = replaceGetManyFilter(q.filter, 'userId', `userId:eq:${user.externalId!}`);
    q.filter = replaceGetManyFilter(q.filter, 'externalUserId', `externalUserId:eq:AgentTest`);

    const sessions = await this.sessionService.getMany(q, {
      toDto: true,
      joinables: ['messages'],
    });
    return sessions;
  }
}
