import { <PERSON>, <PERSON>, Headers, Req } from '@nestjs/common';
import { TelegramTransportService } from './telegram-transport.service';
import { Request } from 'express';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '../../../decorators/public.decorator';

@Controller('transport/telegram')
@ApiTags('Transport/Telegram')
export class TransportTelegramController {
  constructor(private readonly telegramService: TelegramTransportService) {}

  @Post('webhook')
  @Public()
  async webhook(@Headers('x-telegram-bot-api-secret-token') userId: string, @Req() req: Request) {
    await this.telegramService.handleUpdate(userId, req.body);
    return { ok: true };
  }
}
