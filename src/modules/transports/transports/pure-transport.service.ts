import { Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { SessionService } from '../../sessions/session.service';
import { TransportRepository } from '../transport.repository';
import { TransportType } from '../enums/transport-type.enum';
import { SessionStatusType } from '../../..//modules/sessions/enums/status.enum';
import { SessionsDto } from '../../..//modules/sessions/dtos/sessions.dto';
import { FeedbackService } from '../../..//modules/feedback/feedback.service';
import { FeedbackType } from '../../..//modules/feedback/enums/feedback-type.enum';

@Injectable()
export class PureTransportService {
  private readonly logger = new Logger(PureTransportService.name);

  constructor(
    private readonly sessionService: SessionService,
    private readonly transportRepo: TransportRepository,
    private readonly feedbackService: FeedbackService,
  ) { }

  async init(userId: string, userExternalId: string): Promise<string> {
    const transport = await this.transportRepo.findOne({
      where: { type: TransportType.PURE },
    });

    if (!transport) {
      throw new NotFoundException(`Transport "${TransportType.PURE}" not configured`);
    }

    const existingSession = await this.sessionService.findActiveSession(userId, transport.id, userExternalId);
    if (existingSession) {
      return existingSession.id;
    }

    const session = await this.sessionService.initSession(userId, userExternalId, transport.id);
    return session.id;
  }

  async send(sessionId: string, content: string): Promise<{ content: string; role: string }> {
    const history = await this.sessionService.continueSession(sessionId, content);

    if (!history?.length) {
      this.logger.error(`Empty history for session ${sessionId}`);
      throw new InternalServerErrorException('No messages found in session');
    }

    const lastMessage = history.at(-1);

    if (!lastMessage?.content) {
      this.logger.error(`Last message missing content in session ${sessionId}`);
      throw new InternalServerErrorException('Unexpected empty message content');
    }

    return {
      content: lastMessage.content,
      role: lastMessage.role,
    };
  }

  async close(sessionId: string): Promise<void> {
    const closed = await this.sessionService.closeSession(sessionId);
    if (!closed) {
      throw new NotFoundException(`Session "${sessionId}" could not be closed`);
    }
  }

  async getSessionStatus(sessionId: string): Promise<SessionStatusType> {
    const status = await this.sessionService.findOne(sessionId);
    if (!status) {
      throw new NotFoundException(`Session "${sessionId}" not found`);
    }
    return status.status as SessionStatusType;
  }

  async activeSession(userId: string, userExternalId: string): Promise<SessionsDto> {
    const transport = await this.transportRepo.findOne({
      where: { type: TransportType.PURE },
    });

    if (!transport) {
      throw new NotFoundException(`Transport "${TransportType.PURE}" not configured`);
    }

    const s = await this.sessionService.findActiveSession(userId, transport.id, userExternalId);

    if (!s) {
      throw new NotFoundException(`Transport "${TransportType.PURE}" not configured`);
    }

    return s.toDto();
  }

  async addFeedback(feedbackType: FeedbackType, messageId: string) {
    return this.feedbackService.createFeedback(feedbackType, messageId)
  }
}
