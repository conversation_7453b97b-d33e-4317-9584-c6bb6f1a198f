import { Body, Controller, Get, Headers, Param, <PERSON>, Post } from '@nestjs/common';
import { PureTransportService } from './pure-transport.service';
import { TransportService } from '../transport.service';
import { initDto } from '../dtos/init.dto';
import { newMessageDto } from '../dtos/new-message.dto';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '../../../decorators/public.decorator';
import { FeedbackType } from '../../../modules/feedback/enums/feedback-type.enum';

@Controller('transport/api')
@ApiTags('Transport/API')
export class TransportPureController {
  constructor(
    private readonly pureService: PureTransportService,
    private readonly transportService: TransportService,
  ) { }

  @Post()
  @Public()
  async init(@Body() dto: initDto, @Headers('api-key') apiKey: string) {
    const transportUser = await this.transportService.getOneTransportUserByApiKey(apiKey);
    const sessionId = await this.pureService.init(transportUser.userId, dto.externalUserId);
    return { sessionId };
  }

  @Patch()
  @Public()
  async send(@Body() dto: newMessageDto, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByApiKey(apiKey);
    const response = await this.pureService.send(dto.sessionId, dto.content);
    return { response };
  }

  @Post('close/:sessionId')
  @Public()
  async close(@Param('sessionId') sessionId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByApiKey(apiKey);
    await this.pureService.close(sessionId);
    return { success: true };
  }

  @Get('active/:externalUserId')
  @Public()
  async getActiveSession(@Param('externalUserId') externalUserId: string, @Headers('api-key') apiKey: string) {
    const transportUser = await this.transportService.getOneTransportUserByApiKey(apiKey);
    const session = await this.pureService.activeSession(transportUser.id, externalUserId);
    return { session };
  }

  @Get('status/:sessionId')
  @Public()
  async getSessionStatus(@Param('sessionId') sessionId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByApiKey(apiKey);
    const status = await this.pureService.getSessionStatus(sessionId);
    return { status };
  }

  @Post("feedback/:messageId/negative")
  @Public()
  async addNegativeFeedback(@Param('messageId') messageId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByApiKey(apiKey);
    const feedback = await this.pureService.addFeedback(FeedbackType.NEGATIVE, messageId)
    return feedback.toDto()
  }

  @Post("feedback/:messageId/positive")
  @Public()
  async addPositiveFeedback(@Param('messageId') messageId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByApiKey(apiKey);
    const feedback = await this.pureService.addFeedback(FeedbackType.POSITIVE, messageId)
    return feedback.toDto()
  }
}
