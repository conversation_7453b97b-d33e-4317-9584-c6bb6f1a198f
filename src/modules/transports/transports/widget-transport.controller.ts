import { Body, Controller, Get, Headers, Param, <PERSON>, Post } from '@nestjs/common';
import { TransportService } from '../transport.service';
import { initDto } from '../dtos/init.dto';
import { newMessageDto } from '../dtos/new-message.dto';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '../../../decorators/public.decorator';
import { WidgetTransportService } from './widget-transport.service';
import { FeedbackType } from '../../../modules/feedback/enums/feedback-type.enum';

@Controller('transport/widget')
@ApiTags('Transport/Widget')
export class WidgetTransportController {
  constructor(
    private readonly widgetService: WidgetTransportService,
    private readonly transportService: TransportService,
  ) { }

  @Post()
  @Public()
  async init(@Body() dto: initDto, @Headers('api-key') apiKey: string) {
    const transportUser = await this.transportService.getOneTransportUserByPublishableKey(apiKey);
    const sessionId = await this.widgetService.init(transportUser.userId, dto.externalUserId);
    return { sessionId };
  }

  @Patch()
  @Public()
  async send(@Body() dto: newMessageDto, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByPublishableKey(apiKey);
    const response = await this.widgetService.send(dto.sessionId, dto.content);
    return { response };
  }

  @Post('close/:sessionId')
  @Public()
  async close(@Param('sessionId') sessionId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByPublishableKey(apiKey);
    await this.widgetService.close(sessionId);
    return { success: true };
  }

  @Get('active/:externalUserId')
  @Public()
  async getActiveSession(@Param('externalUserId') externalUserId: string, @Headers('api-key') apiKey: string) {
    const transportUser = await this.transportService.getOneTransportUserByPublishableKey(apiKey);
    const session = await this.widgetService.activeSession(transportUser.id, externalUserId);
    return { session };
  }

  @Get('status/:sessionId')
  @Public()
  async getSessionStatus(@Param('sessionId') sessionId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByPublishableKey(apiKey);
    const status = await this.widgetService.getSessionStatus(sessionId);
    return { status };
  }

  @Post("feedback/:messageId/negative")
  @Public()
  async addNegativeFeedback(@Param('messageId') messageId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByPublishableKey(apiKey);
    const feedback = await this.widgetService.addFeedback(FeedbackType.NEGATIVE, messageId)
    return feedback.toDto()
  }

  @Post("feedback/:messageId/positive")
  @Public()
  async addPositiveFeedback(@Param('messageId') messageId: string, @Headers('api-key') apiKey: string) {
    await this.transportService.getOneTransportUserByPublishableKey(apiKey);
    const feedback = await this.widgetService.addFeedback(FeedbackType.POSITIVE, messageId)
    return feedback.toDto()
  }
}
