import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { TransportEntity } from './entities/transport.entity';

@Injectable()
export class TransportRepository extends AbstractRepository<TransportEntity> {
  constructor(
    @InjectRepository(TransportEntity)
    repository: Repository<TransportEntity>,
  ) {
    super(repository);
  }
}
