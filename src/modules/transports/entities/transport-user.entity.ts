import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, Unique } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { TransportUserDto } from '../dtos/transport-user.dto';
import { TransportEntity } from './transport.entity';
import { TransportConfigDto } from '../dtos/transport-setting.dto';

@Entity({ name: 'transports_users' })
@Unique("UQ_transport_user", ["userId", "transportId"])
export class TransportUserEntity extends AbstractEntity<TransportUserDto> {
  dtoClass = TransportUserDto;

  @ManyToOne(() => UserEntity, user => user.transports, { onDelete: 'CASCADE' })
  @JoinColumn({name:"user_id"})
  user?: UserEntity;

  @Column()
  userId: string;

  @ManyToOne(() => TransportEntity, transport => transport.users, { onDelete: 'CASCADE' })
  @JoinColumn({name:"transport_id"})
  transport?: TransportEntity;

  @Column()
  transportId: string;

  @Column({ type: 'jsonb', default: {}, nullable: false })
  setting: TransportConfigDto;

  constructor(item?: Partial<TransportUserEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<TransportUserEntity>): void {
    super.assign(item);

    this.setting = item.setting ?? this.setting;
    this.userId = item.userId ?? this.userId
    this.transportId =  item.transportId ?? this.transportId
  }
}
