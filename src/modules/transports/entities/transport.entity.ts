import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>n, ManyToMany, ManyToOne, OneToMany, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { TransportDto } from '../dtos/transport.dto';
import { SessionsEntity } from '../../sessions/entities/sessions.entity';
import { TransportType } from '../enums/transport-type.enum';

@Entity({ name: 'transports' })
export class TransportEntity extends AbstractEntity<TransportDto> {
  dtoClass = TransportDto;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: false })
  description: string;

  @Column({ nullable: false })
  image: string;

  @Column({ nullable: false, type: "enum", enum: TransportType})
  type: TransportType | keyof typeof TransportType;

  @OneToMany(() => UserEntity, (user) => user.transports)
  users: UserEntity[];

  @OneToMany(() => SessionsEntity, (session) => session.transport)
  sessions: SessionsEntity[];

  constructor(item?: Partial<TransportEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<TransportEntity>): void {
    super.assign(item);

    this.name = item.name ?? this.name;
    this.description = item.description ?? this.description;
    this.image = item.image ?? this.image;
  }
}
