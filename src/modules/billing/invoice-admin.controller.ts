import { <PERSON>, Get, Param, Post, Body, Patch } from '@nestjs/common';
import { InvoiceService } from './invoice.service';
import { CreateInvoiceDto } from './dtos/create-invoice.dto';
import { InvoiceDto } from './dtos/invoice.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody } from '@nestjs/swagger';
import { GuardAdmin } from '../../decorators/guard-admin.decorator';

@Controller('admin/invoices')
@ApiTags('Invoices(Admin)')
export class InvoiceAdminController {
  constructor(private readonly invoiceService: InvoiceService) {}

  @Post()

  @ApiOperation({ summary: 'Create a new invoice' })
  @ApiBody({ type: CreateInvoiceDto })
  @ApiResponse({ status: 201, description: 'Invoice created successfully', type: InvoiceDto })
  @GuardAdmin()
  async createInvoice(@Body() body: CreateInvoiceDto) {
    const invoice = await this.invoiceService.createInvoice(body);
    return invoice.toDto();
  }

  @Patch(':invoiceId/pay')

  @ApiOperation({ summary: 'Mark invoice as paid' })
  @ApiParam({ name: 'invoiceId', description: 'ID of the invoice' })
  @ApiResponse({ status: 200, description: 'Invoice marked as paid', type: InvoiceDto })
  @GuardAdmin()
  async markAsPaid(@Param('invoiceId') invoiceId: string) {
    const updated = await this.invoiceService.markInvoiceAsPaid(invoiceId);
    return updated.toDto();
  }

  @Patch(':invoiceId/cancel')

  @ApiOperation({ summary: 'Cancel an invoice' })
  @ApiParam({ name: 'invoiceId', description: 'ID of the invoice' })
  @ApiResponse({ status: 200, description: 'Invoice cancelled successfully', type: InvoiceDto })
  @GuardAdmin()
  async cancelInvoice(@Param('invoiceId') invoiceId: string) {
    const updated = await this.invoiceService.cancelInvoice(invoiceId);
    return updated.toDto();
  }

  @Get(':invoiceId')

  @ApiOperation({ summary: 'Get invoice by ID' })
  @ApiParam({ name: 'invoiceId', description: 'ID of the invoice' })
  @ApiResponse({ status: 200, description: 'Invoice details', type: InvoiceDto })
  @GuardAdmin()
  async getInvoice(@Param('invoiceId') invoiceId: string) {
    const invoice = await this.invoiceService.getInvoiceById(invoiceId);
    return invoice.toDto();
  }
}
