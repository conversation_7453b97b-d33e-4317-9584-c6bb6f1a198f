import { AbstractEntity } from '../../../common/abstract.entity';
import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';
import { InvoiceEntity } from './invoice.entity';
import { BillingDto } from '../dtos/credit.dto';
import { UserEntity } from '../../../modules/user/entities/user.entity';
import { CreditStatus } from '../enums/subscription-status.enum';

@Entity('credit')
export class CreditEntity extends AbstractEntity<BillingDto> {
  dtoClass = BillingDto;

  @OneToOne(() => UserEntity, (user) => user.credit)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @Column()
  userId: string;

  @Column({ default: 0 })
  available: number;

  @Column({ default: 0 })
  used: number;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  startDate: Date;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  endDate: Date;

  @Column({ type: 'enum', enum: CreditStatus, default: CreditStatus.ACTIVE })
  status: CreditStatus;

  @OneToMany(() => InvoiceEntity, (i) => i.credit)
  invoices: InvoiceEntity[];

  constructor(item?: Partial<CreditEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<CreditEntity>): void {
    super.assign(item);

    this.userId = item.userId ?? this.userId;
    this.available = item.available ?? this.available;
    this.startDate = item.startDate ?? this.startDate;
    this.endDate = item.endDate ?? this.endDate;
    this.status = item.status ?? this.status;
    this.used = item.used ?? this.used;
  }
}
