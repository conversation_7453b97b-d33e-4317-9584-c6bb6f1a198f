import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { CreditEntity } from './entities/credit.entity';

@Injectable()
export class CreditRepository extends AbstractRepository<CreditEntity> {
  constructor(
    @InjectRepository(CreditEntity)
    repository: Repository<CreditEntity>,
  ) {
    super(repository);
  }
}
