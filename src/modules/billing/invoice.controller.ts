import { Controller, Get, Param } from '@nestjs/common';
import { InvoiceService } from './invoice.service';
import { InvoiceDto } from './dtos/invoice.dto';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { GuardUser } from '../../decorators/guard-user.decorator';

@Controller('invoices')
@ApiTags('Invoices')
export class InvoiceController {
  constructor(private readonly invoiceService: InvoiceService) {}

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get all invoices for a user' })
  @ApiParam({ name: 'userId', description: 'ID of the user' })
  @ApiResponse({ status: 200, description: 'List of user invoices', type: [InvoiceDto] })
  @GuardUser()
  async getUserInvoices(@Param('userId') userId: string) {
    const invoices = await this.invoiceService.getUserInvoices(userId);
    return invoices.toDtos();
  }
}
