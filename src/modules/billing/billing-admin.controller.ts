import { Controller, Get, Param, <PERSON>, Body, UseGuards } from '@nestjs/common';
import { BillingService } from './billing.service';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { CreditChangeDto } from './dtos/credit-change.dto';
import { BillingDto } from './dtos/credit.dto';
import { GuardAdmin } from '../../decorators/guard-admin.decorator';

@Controller('admin/billing')
@ApiTags('Billing(Admin)')
export class BillingAdminController {
  constructor(private readonly billingService: BillingService) {}

  @Get(':userId')
  @ApiOperation({
    summary: 'Get credit for a specific user (admin only)',
    description: 'Retrieves the available credit balance of a specified user. Admin access required.',
  })
  @ApiParam({ name: 'userId', description: 'The unique identifier of the user' })
  @ApiResponse({ status: 200, description: 'Successfully retrieved user credit', type: Number })
  @GuardAdmin()
  async getCreditAdmin(@Param('userId') userId: string): Promise<number> {
    const available = await this.billingService.getUserCredit(userId);
    return available;
  }

  @Patch('/:userId/add')
  @ApiOperation({
    summary: 'Add credit to a user account (admin only)',
    description: 'Increases the available credit of a specified user by the provided amount. Admin access required.',
  })
  @ApiParam({ name: 'userId', description: 'The unique identifier of the user' })
  @ApiBody({ type: CreditChangeDto, description: 'The amount of credit to be added' })
  @ApiResponse({ status: 200, description: 'Credit successfully added', type: BillingDto })
  @GuardAdmin()
  async addCredit(@Param('userId') userId: string, @Body() body: CreditChangeDto): Promise<BillingDto> {
    const result = await this.billingService.addCredit(userId, body.amount);
    return result.toDto();
  }

  @Patch(':userId/consume')
  @ApiOperation({
    summary: 'Consume user credit (admin only)',
    description: 'Deducts the specified amount of credit from a user’s available balance. Admin access required.',
  })
  @ApiParam({ name: 'userId', description: 'The unique identifier of the user' })
  @ApiBody({ type: CreditChangeDto, description: 'The amount of credit to be consumed' })
  @ApiResponse({ status: 200, description: 'Credit successfully consumed', type: BillingDto })
  @GuardAdmin()
  async consumeCredit(@Param('userId') userId: string, @Body() body: CreditChangeDto): Promise<BillingDto> {
    const result = await this.billingService.consumeCredit(userId, body.amount);
    return result.toDto();
  }

  @Patch(':userId/reset')
  @ApiOperation({
    summary: 'Reset user credit balance to zero (admin only)',
    description: 'Resets the available credit of the specified user to zero. Admin access required.',
  })
  @ApiParam({ name: 'userId', description: 'The unique identifier of the user' })
  @ApiResponse({ status: 200, description: 'Credit successfully reset', type: BillingDto })
  @GuardAdmin()
  async resetCredit(@Param('userId') userId: string): Promise<BillingDto> {
    const result = await this.billingService.resetCredit(userId);
    return result.toDto();
  }
}
