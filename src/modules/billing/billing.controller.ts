import { Controller, Get, Query } from '@nestjs/common';
import { BillingService } from './billing.service';
import { ApiTags, ApiOperation, ApiResponse, ApiExtraModels } from '@nestjs/swagger';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { CreditEntity } from './entities/credit.entity';
import { GuardUser } from '../../decorators/guard-user.decorator';
import { BillingDto } from './dtos/credit.dto';
import { ApiGetManyDto } from '../../decorators';
import { replaceGetManyFilter } from '../../common/utils';

@Controller('billing')
@ApiTags('Billing')
export class BillingController {
  constructor(private readonly billingService: BillingService) {}

  @Get('/credit')
  @ApiOperation({
    summary: 'Get credit for authenticated user',
    description: 'Returns the current credit balance of the logged-in user.',
  })
  @ApiResponse({ status: 200, description: 'Successfully retrieved credit', type: Number })
  @GuardUser()
  async getCredit(@AuthUser() user: User): Promise<number> {
    const available = await this.billingService.getUserCredit(user.externalId!);
    return available;
  }

  @Get()
  @ApiOperation({
    summary: 'List user Billings',
    description: 'Returns paginated list of Billing activity for the authenticated user.',
  })
  @ApiGetManyDto({ type: BillingDto, description: 'Paginated list of Billing records' })
  @GuardUser()
  async getMany(@AuthUser() user: User, @Query() q: GetManyDto<CreditEntity>) {
    q.filter = replaceGetManyFilter(q.filter, 'userId', `userId:eq:${user.externalId!}`);

    const transports = await this.billingService.getMany(q, {
      toDto: true,
    });
    return transports;
  }
}
