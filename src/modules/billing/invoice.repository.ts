import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { InvoiceEntity } from './entities/invoice.entity';

@Injectable()
export class InvoiceRepository extends AbstractRepository<InvoiceEntity> {
  constructor(
    @InjectRepository(InvoiceEntity)
    repository: Repository<InvoiceEntity>,
  ) {
    super(repository);
  }
}
