import { BadRequestException, Injectable, Logger, ConflictException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { CreditEntity } from './entities/credit.entity';
import { UserService } from '../user/user.service';
import { CreditRepository } from './credit.repository';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { GetManyDtoOptions } from '../../common/abstract.repository';
import { Transactional } from 'typeorm-transactional';
import { v4 as uuidv4 } from 'uuid';
import { CreditLowEvent, NOTIFICATION_EVENTS } from '../mail/interfaces/notification-events.interface';

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name);

  constructor(
    private readonly creditRepo: CreditRepository,
    private readonly userService: UserService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  private isCreditExpired(credit: CreditEntity): boolean {
    return credit.endDate && new Date() > credit.endDate;
  }

  private throwIfExpired(credit: CreditEntity): void {
    if (this.isCreditExpired(credit)) {
      throw new ConflictException('Credit has expired.');
    }
  }

  async getMany<T = CreditEntity>(q: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
    const items = await this.creditRepo.getMany<T>(q, opt);

    return items;
  }

  /**
   * Gets or creates a credit record for the user.
   */
  @Transactional()
  async getOrCreateUserCredit(userId: string): Promise<CreditEntity> {
    const user = await this.userService.findById(userId);
    let credit = await this.creditRepo.findOne({ where: { userId: user.id } });

    if (!credit) {
      credit = this.creditRepo.create({
        userId: user.id,
        available: 0,
        used: 0,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // default 30 days
      });

      try {
        await this.creditRepo.save(credit);
      } catch (e) {
        this.logger.warn(`Credit creation race for user ${user.id}`);
        credit = await this.creditRepo.findOrFail({ where: { userId: user.id } });
      }
    }

    return credit;
  }

  /**
   * Returns available (non-expired) credits for a user.
   */
  async getUserCredit(userId: string): Promise<number> {
    const credit = await this.getOrCreateUserCredit(userId);
    if (this.isCreditExpired(credit)) return 0;
    return credit.available - credit.used;
  }

  /**
   * Adds credit to a user's balance and optionally extend expiry.
   */
  @Transactional()
  async addCredit(userId: string, amount: number, extendDays: number = 0): Promise<CreditEntity> {
    if (amount <= 0) {
      throw new BadRequestException('Amount to add must be greater than zero.');
    }

    const credit = await this.getOrCreateUserCredit(userId);

    if (this.isCreditExpired(credit)) {
      credit.available += amount;
      credit.startDate = new Date();
      credit.endDate = new Date(Date.now() + (extendDays || 30) * 24 * 60 * 60 * 1000);
    } else {
      credit.available += amount;
      if (extendDays > 0) {
        credit.endDate = new Date(Math.max(credit.endDate.getTime(), Date.now() + extendDays * 24 * 60 * 60 * 1000));
      }
    }

    return await this.creditRepo.save(credit);
  }

  /**
   * Consumes credits for a user if they have enough and not expired.
   */
  @Transactional()
  async consumeCredit(userId: string, amount: number): Promise<CreditEntity> {
    if (amount <= 0) {
      throw new BadRequestException('Amount to consume must be greater than zero.');
    }

    const credit = await this.getOrCreateUserCredit(userId);
    this.throwIfExpired(credit);

    if (credit.available - credit.used < amount) {
      throw new ConflictException(
        `Insufficient credits. Available: ${credit.available - credit.used}, required: ${amount}`,
      );
    }

    credit.used += amount;
    const updatedCredit = await this.creditRepo.save(credit);

    // Check if credit level warrants a low credit notification
    await this.checkAndEmitLowCreditEvent(updatedCredit);

    return updatedCredit;
  }

  /**
   * Returns whether the user has enough unexpired credit.
   */
  @Transactional()
  async hasSufficientCredit(userId: string, required: number): Promise<boolean> {
    if (required <= 0) return true;

    const credit = await this.getOrCreateUserCredit(userId);
    return !this.isCreditExpired(credit) && credit.available - credit.used >= required;
  }

  /**
   * Resets user's credit regardless of expiry.
   */
  @Transactional()
  async resetCredit(userId: string): Promise<CreditEntity> {
    const credit = await this.getOrCreateUserCredit(userId);
    credit.available = 0;
    credit.used = 0;
    return await this.creditRepo.save(credit);
  }

  /**
   * Ensures sufficient and unexpired credit or throws.
   */

  async ensureSufficientCreditOrThrow(userId: string, required: number): Promise<void> {
    const credit = await this.getOrCreateUserCredit(userId);
    if (this.isCreditExpired(credit) || credit.available - credit.used < required) {
      throw new ConflictException('Not enough usable credits. Please recharge.');
    }
  }

  /**
   * Checks if credit has expired for user.
   */
  async isUserCreditExpired(userId: string): Promise<boolean> {
    const credit = await this.getOrCreateUserCredit(userId);
    return this.isCreditExpired(credit);
  }

  /**
   * Check credit level and emit low credit event if necessary
   */
  private async checkAndEmitLowCreditEvent(credit: CreditEntity): Promise<void> {
    try {
      const remainingCredits = credit.available - credit.used;
      const totalCredits = credit.available;

      // Skip if no credits or already at zero
      if (totalCredits <= 0 || remainingCredits <= 0) {
        return;
      }

      const percentageThreshold = Math.ceil(totalCredits * 0.1);
      const absoluteThreshold = 100;
      const threshold = Math.min(percentageThreshold, absoluteThreshold);

      if (remainingCredits <= threshold) {
        const user = await this.userService.findById(credit.userId);
        const usagePercentage = ((totalCredits - remainingCredits) / totalCredits) * 100;

        const event: CreditLowEvent = {
          timestamp: new Date(),
          eventId: uuidv4(),
          userId: credit.userId,
          user,
          credit,
          remainingCredits,
          totalCredits,
          usagePercentage,
          threshold,
        };

        this.eventEmitter.emit(NOTIFICATION_EVENTS.CREDIT_LOW, event);

        this.logger.log(
          `Low credit event emitted for user ${credit.userId}: ${remainingCredits}/${totalCredits} credits remaining`,
        );
      }
    } catch (error) {
      this.logger.error(`Failed to check credit level for user ${credit.userId}: ${error.message}`, error.stack);
    }
  }
}
