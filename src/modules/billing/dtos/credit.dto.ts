import { U<PERSON><PERSON>ield, NumberField, DateField, EnumField } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { InvoiceDto } from './invoice.dto';
import { CreditEntity } from '../entities/credit.entity';
import { CreditStatus } from '../enums/subscription-status.enum';

export class BillingDto extends AbstractDto {
  @UUIDField()
  userId: string;

  @NumberField()
  available: number;

  @NumberField()
  used: number;

  invoices?: InvoiceDto[];

  @DateField()
  startDate: Date;

  @DateField()
  endDate: Date;

  @EnumField(() => CreditStatus)
  status: CreditStatus | keyof typeof CreditStatus;

  constructor(entity: CreditEntity) {
    super(entity);

    this.userId = entity.userId;
    this.available = entity.available;
    this.invoices = entity.invoices?.toDtos();
    this.startDate = entity.startDate;
    this.endDate = entity.endDate;
    this.status = entity.status;
    this.used = entity.used;
  }
}
