import { <PERSON><PERSON><PERSON>, EnumField, DateField, UUIDField, NumberField } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { BillingDto } from './credit.dto';
import { InvoiceStatus } from '../enums/invoice-status.enum';
import { InvoiceEntity } from '../entities/invoice.entity';
import { PaymentProvider } from '../enums/payment-provider.enum';

export class InvoiceDto extends AbstractDto {
  @UUIDField()
  creditId: string;

  credit?: BillingDto;

  @NumberField()
  amount: number;

  @DateField()
  issuedAt: Date;

  @DateField({ nullable: true })
  paidAt: Date;

  @EnumField(() => InvoiceStatus)
  status: InvoiceStatus | keyof typeof InvoiceStatus;

  @EnumField(() => PaymentProvider)
  paymentProvider: PaymentProvider;

  @StringField({ nullable: true })
  paymentMethod?: string;

  @StringField({ nullable: true })
  description?: string;

  constructor(entity: InvoiceEntity) {
    super(entity);
    this.creditId = entity.creditId;
    this.amount = entity.amount;
    this.issuedAt = entity.issuedAt;
    this.paidAt = entity.paidAt;
    this.status = entity.status;
    this.paymentProvider = entity.paymentProviderId;
    this.paymentMethod = entity.paymentMethod;
    this.description = entity.description;
    this.credit = entity.credit?.toDto();
  }
}
