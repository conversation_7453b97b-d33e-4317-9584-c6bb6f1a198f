import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CreditEntity } from './entities/credit.entity';
import { InvoiceEntity } from './entities/invoice.entity';
import { InvoiceService } from './invoice.service';
import { BillingService } from './billing.service';
import { InvoiceRepository } from './invoice.repository';
import { CreditRepository } from './credit.repository';
import { InvoiceController } from './invoice.controller';
import { BillingController } from './billing.controller';
import { UserModule } from '../user/user.module';
import { BillingAdminController } from './billing-admin.controller';
import { InvoiceAdminController } from './invoice-admin.controller';

@Module({
  imports: [
    UserModule,
    TypeOrmModule.forFeature([CreditEntity, InvoiceEntity])
  ],
  providers: [InvoiceService, BillingService, InvoiceRepository, CreditRepository],
  controllers: [InvoiceController, BillingController, BillingAdminController, InvoiceAdminController],
  exports: [BillingService, InvoiceService, CreditRepository, InvoiceRepository],
})
export class BillingModule {}
