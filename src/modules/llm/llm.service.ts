import { Injectable, Logger } from '@nestjs/common';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { createOpenAICompatible, type OpenAICompatibleProvider } from '@ai-sdk/openai-compatible';
import { type CoreMessage, generateText, LanguageModelUsage, type LanguageModelV1, type ToolSet } from 'ai';
import { LangfuseExporter } from 'langfuse-vercel';

import { ApiConfigService } from '../../shared/services/api-config.service';
import { PromptSettingDto } from '../prompt/dtos/setting.dto';

@Injectable()
export class LlmService {
  private model: LanguageModelV1;
  private sdk: NodeSDK;
  private exporter: LangfuseExporter;
  private provider: OpenAICompatibleProvider;

  constructor(private readonly envConfig: ApiConfigService) {
    this.provider = createOpenAICompatible({
      name: this.envConfig.openAiConfig.model,
      apiKey: this.envConfig.openAiConfig.apiKey,
      baseURL: this.envConfig.openAiConfig.url,
    });
    this.model = this.provider(this.envConfig.openAiConfig.model);

    this.exporter = new LangfuseExporter({
      secretKey: this.envConfig.langfuseConfig.secretKey,
      publicKey: this.envConfig.langfuseConfig.publicKey,
      baseUrl: this.envConfig.langfuseConfig.baseUrl,
      flushAt: 1,
      flushInterval: 1000,
      /* debug: true // useful for troubleshooting */
    });

    this.sdk = new NodeSDK({
      traceExporter: this.exporter,
      instrumentations: [getNodeAutoInstrumentations()],
    });
    this.sdk.start();
  }

  async generateText(
    messages: CoreMessage[],
    systemPrompt: string,
    tools?: ToolSet,
    promptConfig?: PromptSettingDto,
    openTelemetryMetadataArgs?: {
      sessionId?: string;
      userId?: string;
    },
  ): Promise<{
    text: string;
    usage: LanguageModelUsage;
    toolCallCount: number;
  }> {
    try {
      const telemetryMeta: Record<string, string> = {};
      if (openTelemetryMetadataArgs?.sessionId) telemetryMeta.sessionId = openTelemetryMetadataArgs.sessionId;
      if (openTelemetryMetadataArgs?.userId) telemetryMeta.userId = openTelemetryMetadataArgs.userId;

      const { text, usage, toolCalls } = await generateText({
        model: this.model,
        messages,
        toolChoice: 'auto',
        system: systemPrompt,
        tools,
        maxSteps: 10,
        temperature: promptConfig?.sampling?.temp ?? 0.4,
        topK: promptConfig?.sampling?.topK ?? 5,
        topP: promptConfig?.sampling?.topP ?? 0.3,
        maxTokens: promptConfig?.outputControl?.maxTokens ?? 800,
        presencePenalty: promptConfig?.outputControl?.presencePenalty ?? 1,
        frequencyPenalty: promptConfig?.outputControl?.frequencyPenalty ?? 0,
        experimental_telemetry: {
          isEnabled: true,
          metadata: telemetryMeta,
          functionId: 'llmService.generateText',
        },
      });

      await this.exporter.forceFlush();

      return {
        text,
        usage,
        toolCallCount: toolCalls.length ?? 0,
      };
    } catch (error) {
      Logger.error('generateText error', error);
      throw error;
    }
  }
}
