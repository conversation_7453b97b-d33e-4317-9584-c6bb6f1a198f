import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserDto } from '../dtos/user.dto';
import { UserSettingDto, UserSettingVariables } from '../dtos/user-setting.dto';
import { PromptEntity } from '../../prompt/entities/prompt.entity';
import { ToolsEntity } from '../../tools/entities/tools.entity';
import { TransportEntity } from '../../transports/entities/transport.entity';
import { SessionsEntity } from '../../sessions/entities/sessions.entity';
import { CreditEntity } from '../../../modules/billing/entities/credit.entity';
import { KnowledgeBaseEntity } from '../../../modules/knowledge-base/entities/knowledge-base.entity';
import { TransportUserEntity } from '../../../modules/transports/entities/transport-user.entity';

@Entity({ name: 'users' })
export class UserEntity extends AbstractEntity<UserDto> {
  dtoClass = UserDto;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'jsonb', default: {}, nullable: false })
  setting: UserSettingDto;

  @Column({ unique: true })
  email: string;

  @Column({ type: 'varchar', nullable: true })
  avatar: string;

  @OneToMany(() => PromptEntity, (prompt) => prompt.user)
  prompts: PromptEntity[];

  @OneToMany(() => ToolsEntity, (tool) => tool.user)
  tools: ToolsEntity[];

  @OneToMany(() => TransportUserEntity, (transport) => transport.user)
  transports: TransportUserEntity[];

  @OneToMany(() => SessionsEntity, (session) => session.user)
  sessions: SessionsEntity[];

  @OneToOne(() => CreditEntity, (subs) => subs.user, { nullable: true })
  @JoinColumn({ name: 'credit_id' })
  credit?: CreditEntity;

  @Column({ nullable: true })
  creditId: string;

  @OneToMany(() => KnowledgeBaseEntity, (k) => k.user)
  KnowledgeBase: KnowledgeBaseEntity[];

  constructor(item?: Partial<UserEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<Omit<UserEntity, 'setting'> & { settingVariables: UserSettingVariables }>): void {
    super.assign(item);

    this.name = item.name ?? this.name;
    this.email = item.email ?? this.email;
    this.creditId = item.creditId ?? this.creditId;

    this.setting = {
      variables: {
        agentName: item.settingVariables?.agentName ?? this.setting.variables?.agentName,
        businessName: item.settingVariables?.businessName ?? this.setting.variables?.businessName,
        businessType: item.settingVariables?.businessType ?? this.setting.variables?.businessType,
        businessDescription: item.settingVariables?.businessDescription ?? this.setting.variables?.businessDescription,
      },
    };
  }
}
