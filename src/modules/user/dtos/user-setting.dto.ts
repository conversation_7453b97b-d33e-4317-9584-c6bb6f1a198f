import { <PERSON><PERSON><PERSON>, StringField, StringFieldOptional } from '../../../decorators';

export class UserSettingVariables {
  [key: string]: string;

  @StringFieldOptional()
  businessName: string;

  @StringFieldOptional()
  businessType: string;

  @StringFieldOptional()
  agentName: string;

  @StringFieldOptional()
  businessDescription: string
}
export class UserSettingDto {
  @ClassField(() => UserSettingVariables)
  variables: UserSettingVariables;
}
