import { <PERSON>Field, StringField } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { UserSettingDto } from './user-setting.dto';
import { UserEntity } from '../entities/user.entity';
import { PromptDto } from '../../prompt/dtos/prompt.dto';
import { ToolsDto } from '../../tools/dtos/tools.dto';
import { TransportDto } from '../../transports/dtos/transport.dto';

export class UserDto extends AbstractDto {
  @StringField()
  name: string;

  @StringField()
  email: string;

  @StringField()
  avatar: string;

  @StringField()
  slug?: string;

  setting: UserSettingDto;

  prompts?: PromptDto[];

  tools?: ToolsDto[];

  transports?: TransportDto[];

  constructor(e: UserEntity) {
    super(e);

    this.name = e.name;
    this.setting = e.setting;
    this.email = e.email;
    this.avatar = e.avatar;
  }
}
