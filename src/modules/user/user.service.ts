import { Injectable, NotFoundException } from '@nestjs/common';
import { UserRepository } from './user.repository';
import { CreateUserDto } from './dtos/create-user.dto';
import { UserSettingVariables } from './dtos/user-setting.dto';
import { Raw } from 'typeorm';
import { TransportType } from '../transports/enums/transport-type.enum';

@Injectable()
export class UserService {
  constructor(private readonly repo: UserRepository) {}

  create(args: Omit<CreateUserDto, 'setting'>) {
    const user = this.repo.create();
    user.assign({
      email: args.email,
      name: args.name,
      avatar: args.avatar,
      settingVariables: {
        agentName: 'My Agent',
        businessName: 'My business',
        businessType: 'My business type',
        businessDescription: "This is a business description"
      },
    });

    return this.repo.save(user);
  }

  async findById(id: string) {
    const user = await this.repo.findOne({
      where: {
        id,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async setSettings(id: string, agrs: UserSettingVariables) {
    const user = await this.findById(id);

    user?.assign({
      settingVariables: agrs,
    });

    return this.repo.save(user);
  }

  async getSettings(id: string) {
    const user = await this.findById(id);

    return user.setting.variables;
  }

  async getUserBusinessPublicInfo(businessName: string) {
    const normalizedSlug = businessName.replaceAll('-', ' ').toLowerCase();

    const business = await this.repo.findOrFail({
      where: {
        setting: Raw((alias) => `LOWER((${alias}->'variables'->>'businessName')) = :name`, { name: normalizedSlug }),
      },
      // relations: {
      //   transports: {
      //     transport: {
      //       type: TransportType.SLACK,
      //     },
      //   },
      // },
    });

    return {
      info: business.setting.variables,
      apiKey: business.transports[0]?.setting.pure?.apiKey
    };
  }
}
