import { Body, Controller, Get, Param, Patch, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserService } from './user.service';
import { UserSettingVariables } from './dtos/user-setting.dto';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { User } from '@clerk/backend';
import { RolesGuard } from '../auth/guards/clerk-role.guard';
import { ApiGetManyDto } from '../../decorators';
import { Public } from '../../decorators/public.decorator';

@Controller('user')
@ApiTags('User')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Patch('setting')
  @ApiOperation({
    summary: 'Update user settings',
    description: 'Updates user preferences and configuration settings.',
  })
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'User settings updated successfully' })
  @UseGuards(new RolesGuard(['USER']))
  async setUserSetting(@AuthUser() user: User, @Body() args: UserSettingVariables) {
    const userSetting = await this.userService.setSettings(user.externalId!, args);
    return userSetting.toDto();
  }

  @Get('setting')
  @ApiOperation({
    summary: 'Get user settings',
    description: 'Fetches the current settings of the authenticated user.',
  })
  @ApiBearerAuth()
  @ApiResponse({ status: 200, description: 'User settings retrieved successfully', type: UserSettingVariables })
  @ApiGetManyDto({ type: UserSettingVariables, description: 'User settings retrieved successfully' })
  @UseGuards(new RolesGuard(['USER']))
  async getUserSetting(@AuthUser() user: User) {
    return this.userService.getSettings(user.externalId!);
  }

  @Get('public-info/:businessName')
  @ApiOperation({
    summary: 'Get business public info',
    description: 'Fetches the public info of business.',
  })
  @ApiGetManyDto({ type: UserSettingVariables, description: 'User settings retrieved successfully' })
  @Public()
  async publicInfo(@Param('businessName') businessName: string) {
    return this.userService.getUserBusinessPublicInfo(businessName);
  }
}
