import { Inject, Injectable } from '@nestjs/common';

import { IClerkCreatedUserData } from './interfaces/clerk-user-created-payload.interface';
import { UserService } from '../user/user.service';
import { ClerkClient } from '@clerk/backend';
import { PromptService } from '../prompt/prompt.service';
import { promptType } from '../prompt/enums/prompt-type.enum';
import { BillingService } from '../billing/billing.service';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export default class AuthService {
  constructor(
    private readonly usersService: UserService,
    private readonly promptService: PromptService,
    private readonly billingService: BillingService,
    @Inject('ClerkClient') private clerkClient: ClerkClient,
  ) {}

  @Transactional()
  async createdUserHandler(arg: IClerkCreatedUserData) {
    const name = `${arg.first_name ?? 'User'} ${arg.last_name ?? ''}`.trim();
    const user = await this.usersService.create({
      avatar: arg.image_url,
      email: arg.email_addresses[0]?.email_address!,
      name: name,
    });

    const { content, setting } = await this.promptService.findDefaultCustomerSupportPrompt();
    await this.promptService.createPrompt({
      userId: user.id,
      content,
      type: promptType.COSTUMER_SUPPORT,
      setting: setting,
    });

    const billing = await this.billingService.getOrCreateUserCredit(user.id);
    if (billing.available == 0) {
      await this.billingService.addCredit(user.id, 50);
    }

    await this.clerkClient.users.updateUser(arg.id, {
      externalId: user.id,
    });

    await this.clerkClient.users.updateUserMetadata(arg.id, {
      publicMetadata: {
        role: 'USER',
      },
    });
  }
}
