import { Module } from '@nestjs/common';

import { ApiConfigService } from '../../shared/services/api-config.service';
import AuthController from './auth.controller';
import AuthService from './auth.service';

import { createClerkClient } from '@clerk/backend';
import { ClerkStrategy } from './strategies/clerk.strategy';
import { APP_GUARD } from '@nestjs/core';
import { ClerkAuthGuard } from './guards/clerk-auth.guard';
import { UserModule } from '../user/user.module';
import { PromptModule } from '../prompt/prompt.module';
import { BillingModule } from '../billing/billing.module';

@Module({
  imports: [UserModule, PromptModule, BillingModule],
  providers: [
    AuthService,
    {
      provide: 'ClerkClient',
      useFactory: (configService: ApiConfigService) => {
        return createClerkClient({
          publishableKey: configService.clerkConfig.publishableKey,
          secretKey: configService.clerkConfig.secretKey,
        });
      },
      inject: [ApiConfigService],
    },
    {
      provide: APP_GUARD,
      useClass: ClerkAuthGuard,
    },
    ClerkStrategy,
  ],
  controllers: [AuthController],
  exports: [],
})
export default class AuthModule {}
