import { Body, Controller, Headers, HttpCode, Post, UnauthorizedException } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import AuthService from './auth.service';
import { Webhook } from 'svix';
import { ClerkWebhookPayload } from './type/clerk-webhook-payload.type';
import { IClerkCreatedUserData } from './interfaces/clerk-user-created-payload.interface';
import { ApiConfigService } from '../../shared/services/api-config.service';
import { Public } from '../../decorators/public.decorator';

@ApiTags('Auth')
@Controller('auth')
export default class AuthController {
  constructor(
    private readonly config: ApiConfigService,
    private readonly authService: AuthService,
  ) {}

  @Post('webhook')
  @HttpCode(200)
  @Public()
  async handleClerkEvent(
    @Body() body: any,
    @Headers('svix-id') svixId: string,
    @Headers('svix-signature') svixSignature: string,
    @Headers('svix-timestamp') svixTimestamp: string,
  ) {
    const payload = body.toString('utf8');
    const headers = {
      'svix-id': svixId,
      'svix-signature': svixSignature,
      'svix-timestamp': svixTimestamp,
    };

    const wh = new Webhook(this.config.clerkConfig.webhookSecret);

    let evt: ClerkWebhookPayload;
    try {
      evt = wh.verify(payload, headers) as ClerkWebhookPayload;
    } catch (err) {
      throw new UnauthorizedException('Invalid webhook signature');
    }

    const eventType = evt.type;

    if (eventType === 'user.created') {
      const user = evt.data as IClerkCreatedUserData;
      this.authService.createdUserHandler(user);
    }

    return { received: true };
  }
}
