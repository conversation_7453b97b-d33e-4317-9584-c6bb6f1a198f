import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private readonly roles: string[]) {}

  canActivate(ctx: ExecutionContext): boolean {
    const req = ctx.switchToHttp().getRequest();
    const user = req.user; // Clerk User object
    const userRole = user.publicMetadata?.role ?? "user"; // e.g. 'admin'
    if (!userRole || !this.roles.includes(userRole)) {
      throw new ForbiddenException();
    }
    return true;
  }
}
