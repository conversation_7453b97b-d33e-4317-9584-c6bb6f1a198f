export interface IClerkCreatedUserData {
    id: string;
    object: 'user';
    username: string | null;
    first_name: string | null;
    last_name: string | null;
    email_addresses: {
        id: string;
        object: 'email_address';
        email_address: string;
        verification: {
            status: 'verified' | 'unverified' | 'pending' | null;
            strategy: string | null;
        };
        linked_to: any[];
    }[];
    phone_numbers: {
        id: string;
        object: 'phone_number';
        phone_number: string;
        reserved: boolean;
        verification: {
            status: string;
            strategy: string | null;
        };
        linked_to: any[];
    }[];
    external_accounts: {
        id: string;
        object: 'external_account';
        approved_scopes: string;
        email_address: string;
        provider: string;
        identification_id: string;
        provider_user_id: string;
        public_metadata: Record<string, any>;
        label: string | null;
        scopes: string[];
    }[];
    image_url: string;
    profile_image_url: string;
    public_metadata: Record<string, any>;
    private_metadata: Record<string, any>;
    unsafe_metadata: Record<string, any>;
    created_at: number; // Unix timestamp in milliseconds
    updated_at: number; // Unix timestamp in milliseconds
    last_sign_in_at: number | null;
    primary_email_address_id: string | null;
    primary_phone_number_id: string | null;
    primary_web3_wallet_id: string | null;
    password_enabled: boolean;
    two_factor_enabled: boolean;
    email_verified: boolean;
}
