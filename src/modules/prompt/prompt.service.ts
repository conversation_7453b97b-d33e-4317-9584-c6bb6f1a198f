import { Injectable, NotFoundException } from '@nestjs/common';
import { PromptRepository } from './prompt.repository';
import { CreatePromptDto } from './dtos/create-prompt.dto';
import { UserService } from '../user/user.service';
import { promptType } from './enums/prompt-type.enum';
import { UpdateUserPromptDto } from './dtos/update-user-prompt.dto';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class PromptService {
  constructor(
    private readonly repo: PromptRepository,
    private readonly userService: UserService,
  ) {}

  async createPrompt(args: CreatePromptDto) {
    await this.userService.findById(args.userId);
    const p = this.repo.create();
    p.assign({
      content: args.content,
      setting: args.setting,
      type: args.type,
      userId: args.userId,
    });

    return this.repo.save(p);
  }

  async getUserPrompts(userId: string) {
    return this.repo.findAll({
      where: {
        userId: userId,
      },
    });
  }

  @Transactional()
  async updateUserPrompt(id: string, userId: string, args: UpdateUserPromptDto) {
    const p = await this.repo.findOne({
      where: {
        id,
        userId,
      },
    });

    if (!p) {
      throw new NotFoundException('prompt not found');
    }

    p.assign(args);

    return this.repo.save(p);
  }

  async findOne(id: string) {
    const p = await this.repo.findOne({
      where: {
        id,
      },
    });

    if (!p) {
      throw new NotFoundException('prompt not found');
    }

    return p;
  }

  async getRDEPrompt() {
    const p = await this.repo.findOne({
      where: {
        type: 'DATA_EXTRACTOR',
      },
    });

    if (!p) {
      throw new NotFoundException('prompt not found');
    }

    return p;
  }

  async findOneCostumerSupportByUserId(userId: string) {
    const user = await this.userService.findById(userId);

    const p = await this.repo.findOne({
      where: {
        userId,
        type: promptType.COSTUMER_SUPPORT,
      },
    });

    if (!p) {
      throw new NotFoundException('prompt not found');
    }

    return p;
  }

  async findDefaultCustomerSupportPrompt() {
    const prompt = await this.repo.findOne({
      where: {
        type: promptType.DEFAULT_CUSTOMER_SUPPORT,
      },
    });

    if (!prompt) {
      throw new NotFoundException('prompt not found');
    }

    return prompt;
  }
}
