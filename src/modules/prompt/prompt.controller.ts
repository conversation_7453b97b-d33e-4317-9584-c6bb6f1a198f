import { Body, Controller, Get, Param, Patch, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { PromptService } from './prompt.service';
import { CreatePromptDto } from './dtos/create-prompt.dto';
import { UpdateUserPromptDto } from './dtos/update-user-prompt.dto';
import { PromptDto } from './dtos/prompt.dto';
import { RolesGuard } from '../auth/guards/clerk-role.guard';

@Controller('prompts')
@ApiTags('Prompts(Admin)')
export class PromptController {
  constructor(private readonly promptService: PromptService) {}

  @Post('')
  @ApiOperation({
    summary: 'Create a new global prompt',
    description: 'Creates a new prompt available system-wide. Only accessible by admin users.',
  })
  @ApiResponse({ status: 201, description: 'Prompt created successfully', type: PromptDto })
  @ApiBearerAuth()
  @UseGuards(new RolesGuard(['ADMIN']))
  async newPrompt(@Body() args: CreatePromptDto): Promise<PromptDto> {
    const p = await this.promptService.createPrompt(args);
    return p.toDto();
  }

  @Get('user/:userId')
  @ApiOperation({
    summary: 'Get prompts for a specific user',
    description: 'Returns a list of prompts assigned to a specific user. Admin only.',
  })
  @ApiParam({ name: 'userId', description: 'The ID of the user to fetch prompts for' })
  @ApiResponse({ status: 200, description: 'List of user prompts', type: [PromptDto] })
  @ApiBearerAuth()
  @UseGuards(new RolesGuard(['ADMIN']))
  async getUserPrompt(@Param('userId') userId: string) {
    const p = await this.promptService.getUserPrompts(userId);
    return p.toDtos();
  }

  @Patch(':promptId/user/:userId')
  @ApiOperation({
    summary: 'Update a user-assigned prompt',
    description: 'Updates a specific prompt assigned to a user. Admin access required.',
  })
  @ApiParam({ name: 'promptId', description: 'The ID of the prompt to update' })
  @ApiParam({ name: 'userId', description: 'The ID of the user the prompt is assigned to' })
  @ApiResponse({ status: 200, description: 'Prompt updated successfully', type: PromptDto })
  @ApiBearerAuth()
  @UseGuards(new RolesGuard(['ADMIN']))
  async updateUserPrompt(
    @Param('userId') userId: string,
    @Param('promptId') promptId: string,
    @Body() args: UpdateUserPromptDto,
  ): Promise<PromptDto> {
    const p = await this.promptService.updateUserPrompt(promptId, userId, args);
    return p.toDto();
  }
}
