import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { PromptEntity } from './entities/prompt.entity';

@Injectable()
export class PromptRepository extends AbstractRepository<PromptEntity> {
  constructor(
    @InjectRepository(PromptEntity)
    repository: Repository<PromptEntity>,
  ) {
    super(repository);
  }
}
