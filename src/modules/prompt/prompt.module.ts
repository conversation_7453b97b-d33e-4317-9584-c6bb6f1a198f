import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PromptEntity } from './entities/prompt.entity';
import { PromptRepository } from './prompt.repository';
import { PromptService } from './prompt.service';
import { PromptController } from './prompt.controller';
import { UserModule } from '../user/user.module';

@Module({
  imports: [TypeOrmModule.forFeature([PromptEntity]), UserModule],
  providers: [PromptService, PromptRepository],
  controllers: [PromptController],
  exports: [PromptService, PromptRepository],
})
export class PromptModule {}
