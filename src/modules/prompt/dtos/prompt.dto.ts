import { <PERSON><PERSON><PERSON>, <PERSON>ail<PERSON><PERSON>, Enum<PERSON><PERSON>, StringField, UUIDField, UUIDFieldOptional } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import type { PromptEntity } from '../entities/prompt.entity';
import { UserDto } from '../../user/dtos/user.dto';
import { promptType } from '../enums/prompt-type.enum';
import { PromptSettingDto } from './setting.dto';

export class PromptDto extends AbstractDto {
    @EnumField(() => promptType)
    type: promptType | keyof typeof promptType;

    @StringField()
    content: string;

    user?: UserDto;

    @UUIDFieldOptional()
    userId?: string

    // @ClassField(() => PromptSettingDto)
    setting: PromptSettingDto;

    constructor(e: PromptEntity) {
        super(e);

        this.type = e.type;
        this.content = e.content;
        this.user = e.user?.toDto();
        this.userId = e.userId;
        this.setting = e.setting;
    }
}
