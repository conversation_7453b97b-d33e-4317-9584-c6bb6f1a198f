import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToMany, ManyToOne, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { promptType } from '../enums/prompt-type.enum';
import { PromptDto } from '../dtos/prompt.dto';
import { PromptSettingDto } from '../dtos/setting.dto';

@Entity({ name: 'prompts' })
export class PromptEntity extends AbstractEntity<PromptDto> {
  dtoClass = PromptDto;

  @Column({ type: 'enum', enum: promptType })
  type: promptType | keyof typeof promptType;

  @Column({ nullable: false, type: 'varchar' })
  content: string;

  @Column({ type: 'jsonb', default: {}, nullable: false })
  setting: PromptSettingDto;

  @ManyToOne(() => UserEntity, (user) => user.prompts, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: UserEntity;

  @Column({ nullable: true })
  userId?: string;

  constructor(item?: Partial<PromptEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<PromptEntity>): void {
    super.assign(item);

    this.type = item.type ?? this.type;
    this.content = item.content ?? this.content;
    this.setting = item.setting ?? this.setting;
    this.userId = item.userId ?? this.userId;
  }
}
