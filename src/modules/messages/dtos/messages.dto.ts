import { <PERSON>um<PERSON>ield, <PERSON><PERSON>ield, UUI<PERSON>ield } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { MessageEntity } from '../entities/messages.entity';
import { SessionsDto } from '../../sessions/dtos/sessions.dto';
import { MessageRole } from '../enums/role.enum';

export class MessageDto extends AbstractDto {
    @StringField()
    content: string;

    @EnumField(() => MessageRole)
    role: MessageRole | keyof typeof MessageRole;;

    session?: SessionsDto;

    @UUIDField()
    sessionId?: string

    constructor(e: MessageEntity) {
        super(e);

        this.content = e.content;
        this.role = e.role;
        this.session = e.session?.toDto();
        this.sessionId = e.sessionId;
    }
}
