import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToMany, ManyToOne, OneToOne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { MessageDto } from '../dtos/messages.dto';
import { SessionsEntity } from '../../sessions/entities/sessions.entity';
import { MessageRole } from '../enums/role.enum';
import { FeedbackEntity } from '../../../modules/feedback/entities/feedback.entity';

@Entity('messages')
export class MessageEntity extends AbstractEntity<MessageDto> {
  dtoClass = MessageDto;

  @Column({ nullable: false })
  content: string;

  @Column({ type: 'enum', enum: MessageRole })
  role: MessageRole | keyof typeof MessageRole;

  @ManyToOne(() => SessionsEntity, (session) => session.messages)
  @JoinColumn({ name: 'session_id' })
  session?: SessionsEntity;

  @Column()
  sessionId?: string;

  @OneToOne(() => FeedbackEntity, { onDelete: 'CASCADE', nullable: true })
  feedback?: FeedbackEntity;


  constructor(item?: Partial<MessageEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<MessageEntity>): void {
    super.assign(item);

    this.content = item.content ?? this.content;
    this.role = item.role ?? this.role;
  }
}
