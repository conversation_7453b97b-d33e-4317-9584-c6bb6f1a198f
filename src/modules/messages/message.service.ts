import { Injectable } from '@nestjs/common';
import { MessageRepository } from './message.repository';
import { MessageRole } from './enums/role.enum';

@Injectable()
export class MessageService {
  constructor(private readonly repo: MessageRepository) {}

  async create(content: string, role: MessageRole, sessionId: string) {
    const m = this.repo.create({
      content,
      role,
      sessionId,
    });

    return this.repo.save(m);
  }

  async retrieveSessionHistory(sessionId: string) {
    const res = await this.repo.findAll({
      where: {
        sessionId,
      },
      order: {
        createdAt: 'ASC',
      },
      // select: ['content', 'role'],
    });

    return res;
  }
}
