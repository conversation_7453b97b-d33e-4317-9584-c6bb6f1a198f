import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { MessageEntity } from './entities/messages.entity';

@Injectable()
export class MessageRepository extends AbstractRepository<MessageEntity> {
  constructor(
    @InjectRepository(MessageEntity)
    repository: Repository<MessageEntity>,
  ) {
    super(repository);
  }
}
