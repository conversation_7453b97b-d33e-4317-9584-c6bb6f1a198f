import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { AbstractRepository } from '../../common/abstract.repository';
import { ToolsEntity } from './entities/tools.entity';

@Injectable()
export class ToolsRepository extends AbstractRepository<ToolsEntity> {
  constructor(
    @InjectRepository(ToolsEntity)
    repository: Repository<ToolsEntity>,
  ) {
    super(repository);
  }
}
