import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON>ne } from 'typeorm';

import { AbstractEntity } from '../../../common/abstract.entity';
import { UserEntity } from '../../user/entities/user.entity';
import { ToolsDto } from '../dtos/tools.dto';
import { ToolSettingDto } from '../dtos/tools-setting.dto';

@Entity({ name: 'tools' })
export class ToolsEntity extends AbstractEntity<ToolsDto> {
  dtoClass = ToolsDto;

  @Column({ nullable: false })
  name: string;

  @Column({ nullable: false })
  description: string;

  @Column({ type: 'jsonb', default: {}, nullable: false })
  setting: ToolSettingDto;

  @ManyToOne(() => UserEntity, (user) => user.tools, { nullable: true })
  @JoinColumn({ name: 'user_id' })
  user?: UserEntity;

  @Column({ nullable: true })
  userId?: string;

  @Column({ type: 'boolean', default: true })
  isPublic: boolean;

  constructor(item?: Partial<ToolsEntity>) {
    super();

    if (!item) {
      return;
    }

    this.assign(item);
  }

  assign(item: Partial<ToolsEntity>): void {
    super.assign(item);

    this.name = item.name ?? this.name;
    this.description = item.description ?? this.description;
    this.setting = item.setting ?? this.setting;
  }
}
