import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ield, <PERSON><PERSON>ield, UUIDField } from '../../../decorators';
import { AbstractDto } from '../../../common/dto/abstract.dto';
import { UserDto } from '../../user/dtos/user.dto';
import { ToolsEntity } from '../entities/tools.entity';
import { ToolSettingDto } from './tools-setting.dto';
import { ApiProperty } from '@nestjs/swagger';
import { ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ToolsDto extends AbstractDto {
  @StringField()
  name: string;

  @StringField()
  description: string;

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => ToolSettingDto)
  setting: ToolSettingDto;

  user?: UserDto;

  @UUIDField()
  userId?: string;

  @BooleanField()
  isPublic: boolean;

  constructor(e: ToolsEntity) {
    super(e);

    this.name = e.name;
    this.description = e.description;
    this.user = e.user?.toDto();
    this.userId = e.userId;
    this.setting = e.setting;
    this.isPublic = e.isPublic;
  }
}
