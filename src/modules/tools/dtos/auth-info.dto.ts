import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Field, StringFieldOptional } from '../../../decorators';
import { AuthModel } from '../enums/auth-methods.enum';
import { IsArray } from 'class-validator';
import { ApiKeyIn } from '../enums/api-key-in.enum';

export class AuthInfo {
  @EnumField(() => AuthModel)
  model: AuthModel;

  @StringFieldOptional()
  username?: string;

  @StringFieldOptional()
  password?: string;

  @StringFieldOptional()
  token?: string;

  @StringFieldOptional()
  apiKeyName?: string;

  @EnumField(() => ApiKeyIn)
  apiKeyIn?: ApiKeyIn;
}
