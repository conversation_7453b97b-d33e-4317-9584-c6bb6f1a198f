import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BooleanField, ClassField, EnumField, StringField } from '../../../decorators';
import { HttpMethod } from '../enums/http-methods.enum';
import { ParamType } from '../enums/param-type.enum';
import { AuthInfo } from './auth-info.dto';
import { IsJSON, IsObject, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ParamDefinition {
  @EnumField(() => ParamType)
  type: ParamType;

  @BooleanField()
  required?: boolean;

  @StringField()
  description: string;

  @BooleanField()
  isArray: boolean;
}

export class ToolSettingDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  queryParams?: Record<string, ParamDefinition>;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  bodyParams?: Record<string, ParamDefinition>;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  headerParams?: Record<string, ParamDefinition>;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  pathParams?: Record<string, ParamDefinition>;

  @ApiPropertyOptional()
  @IsOptional()
  @IsObject()
  outputSchema?: Record<string, unknown>;

  @StringField()
  endpoint: string;

  @EnumField(() => HttpMethod)
  method: HttpMethod;

  @ApiProperty()
  @ValidateNested({ each: true })
  @Type(() => AuthInfo)
  auth?: AuthInfo;
}
