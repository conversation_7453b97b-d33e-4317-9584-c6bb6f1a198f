import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ToolsEntity } from './entities/tools.entity';
import { ToolsRepository } from './tools.repository';
import { ToolService } from './tools.service';
import { ToolController } from './tools.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ToolsEntity])],
  providers: [ToolsRepository, ToolService],
  controllers: [ToolController],
  exports: [ToolService],
})
export class ToolsModule {}
