import { Controller, Get, Post, Patch, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam } from '@nestjs/swagger';
import { ToolService } from './tools.service';
import { ToolsDto } from './dtos/tools.dto';
import { ToolSettingDto } from './dtos/tools-setting.dto';
import { GuardUser } from '../../decorators/guard-user.decorator';
import { ToolsEntity } from './entities/tools.entity';
import { GetManyDto } from '../../common/dto/get-many.dto';
import { User } from '@clerk/backend';
import { AuthUser } from '../auth/decorators/auth-user.decorator';
import { CreateToolDto } from './dtos/create-tool.dto';
import { replaceGetManyFilter } from '../../common/utils';

@Controller('tools')
@ApiTags('Tools')
export class ToolController {
  constructor(private readonly toolService: ToolService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tool' })
  @ApiResponse({ status: 201, description: 'Tool created', type: ToolsDto })
  @GuardUser()
  async createTool(@AuthUser() user: User, @Body() body: CreateToolDto): Promise<ToolsDto> {
    const { name, description, setting } = body;
    const newTool = await this.toolService.create(user.externalId!, name, description, true, setting);
    return newTool.toDto();
  }

  @Patch(':id')
  @ApiOperation({ summary: "Update an existing tool's settings" })
  @ApiResponse({ status: 200, description: 'Tool updated', type: ToolsDto })
  @GuardUser()
  async updateTool(@Param('id') id: string, @Body() setting: ToolSettingDto): Promise<ToolsDto> {
    const updatedTool = await this.toolService.update(id, setting);
    return updatedTool.toDto();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a tool by ID' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @GuardUser()
  async getTool(@Param('id') id: string): Promise<ToolsDto> {
    const tool = await this.toolService.getById(id);
    return tool.toDto();
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiResponse({ status: 204, description: 'Tool deleted' })
  @GuardUser()
  async deleteTool(@Param('id') id: string): Promise<void> {
    await this.toolService.delete(id);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tools for the current user' })
  @ApiResponse({ status: 200, description: 'List of tools', type: [ToolsDto] })
  @GuardUser()
  async getMany(@AuthUser() user: User, @Query() q: GetManyDto<ToolsEntity>) {
    q.filter = replaceGetManyFilter(q.filter, 'userId', `userId:eq:${user.externalId!}`)
    const tools = await this.toolService.getMany(q, {
      toDto: true,
    });
    return tools;
  }
}
