import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { ToolsEntity } from './entities/tools.entity';
import { ParamDefinition, ToolSettingDto } from './dtos/tools-setting.dto';
import { ToolsRepository } from './tools.repository';
import z, { ZodTypeAny } from 'zod';
import { ParamType } from './enums/param-type.enum';
import { ToolExecutionOptions } from 'ai';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { HttpExecutionResult } from './dtos/http-execution-result.dto';
import { GetManyDto } from 'src/common/dto/get-many.dto';
import { GetManyDtoOptions } from 'src/common/abstract.repository';

@Injectable()
export class ToolService {
  private readonly logger = new Logger(ToolService.name);

  constructor(private readonly repo: ToolsRepository) { }

  generateZodShape(params?: Record<string, ParamDefinition>): Record<string, ZodTypeAny> {
    const shape: Record<string, ZodTypeAny> = {};
    if (!params) return shape;

    for (const [key, def] of Object.entries(params)) {
      let schema = this.paramTypeToZod(def.type);
      if (def.isArray) schema = z.array(schema);
      if (!def.required) schema = schema.optional();
      shape[key] = schema.describe(def.description!);
    }
    return shape;
  }

  paramTypeToZod(type: ParamType): ZodTypeAny {
    switch (type) {
      case 'string':
        return z.string();
      case 'number':
        return z.number();
      case 'boolean':
        return z.boolean();
      case 'date':
        return z.string().datetime();
      default:
        return z.any();
    }
  }

  async getMany<T = ToolsEntity>(q: GetManyDto<T>, opt: GetManyDtoOptions<T>) {
      const items = await this.repo.getMany<T>(q, opt);
      return items;
  }

  private resolvePathParameters(url: string, params: Record<string, any>): string {
    const pathParamRegex = /:([a-zA-Z0-9_]+)/g;
    return url.replace(pathParamRegex, (match, paramName) => {
      if (params[paramName] !== undefined) {
        return encodeURIComponent(String(params[paramName]));
      }
      this.logger.warn(`Path parameter '${paramName}' not found in input parameters`);
      return match;
    });
  }

  private extractQueryParams(args: any, queryParamDefs?: Record<string, ParamDefinition>): Record<string, any> {
    if (!queryParamDefs) return {};

    const queryParams: Record<string, any> = {};
    for (const [key, def] of Object.entries(queryParamDefs)) {
      if (args[key] !== undefined) {
        queryParams[key] = args[key];
      }
    }
    return queryParams;
  }

  private extractBodyParams(args: any, bodyParamDefs?: Record<string, ParamDefinition>): any {
    if (!bodyParamDefs) return undefined;

    const bodyParams: Record<string, any> = {};
    let hasBodyParams = false;

    for (const [key, def] of Object.entries(bodyParamDefs)) {
      if (args[key] !== undefined) {
        bodyParams[key] = args[key];
        hasBodyParams = true;
      }
    }

    return hasBodyParams ? bodyParams : undefined;
  }

  private extractHeaderParams(args: any, headerParamDefs?: Record<string, ParamDefinition>): Record<string, string> {
    if (!headerParamDefs) return {};

    const headers: Record<string, string> = {};
    for (const [key, def] of Object.entries(headerParamDefs)) {
      if (args[key] !== undefined) {
        headers[key] = String(args[key]);
      }
    }
    return headers;
  }

  private async executeHttpRequest(
    setting: ToolSettingDto,
    args: any,
    options: ToolExecutionOptions & { timeoutMs?: number }
  ): Promise<HttpExecutionResult> {
    const startTime = Date.now();

    try {
      // Resolve URL with path parameters
      const resolvedURL = this.resolvePathParameters(setting.endpoint, args);

      // Extract different parameter types
      const queryParams = this.extractQueryParams(args, setting.queryParams);
      const bodyData = this.extractBodyParams(args, setting.bodyParams);
      const headerParams = this.extractHeaderParams(args, setting.headerParams);

      // Build axios request config
      const requestConfig: AxiosRequestConfig = {
        method: setting.method.toLowerCase() as any,
        url: resolvedURL,
        timeout: options.timeoutMs || 30000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'MCP-Tool-Mapper/1.0',
          ...headerParams,
        },
      };

      // Add query parameters
      if (Object.keys(queryParams).length > 0) {
        requestConfig.params = queryParams;
      }

      // Add body data for non-GET requests
      if (bodyData && !['GET', 'HEAD'].includes(setting.method.toUpperCase())) {
        requestConfig.data = bodyData;
      }

      this.logger.debug(`Executing HTTP request: ${setting.method} ${resolvedURL}`, {
        queryParams,
        bodyData,
        headers: headerParams,
      });

      const response: AxiosResponse = await axios(requestConfig);
      const executionTime = Date.now() - startTime;

      return {
        success: true,
        data: response.data,
        status: response.status,
        statusText: response.statusText,
        headers: response.headers as Record<string, string>,
        executionTime,
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;

      if (axios.isAxiosError(error)) {
        this.logger.error(`HTTP request failed: ${error.message}`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
        });

        return {
          success: false,
          error: error.message,
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          executionTime,
        };
      }

      this.logger.error(`Unexpected error during HTTP request: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        executionTime,
      };
    }
  }

  async create(userId: string, name: string, description: string, isPublic: boolean, setting: ToolSettingDto): Promise<ToolsEntity> {
    const tool = this.repo.create({
      name: name,
      description: description,
      userId: userId,
      setting: setting,
      isPublic: isPublic,
    });
    const saved = await this.repo.save(tool);
    return saved;
  }

  async update(id: string, setting: ToolSettingDto): Promise<ToolsEntity> {
    const tool = await this.getById(id);
    tool.setting = setting;
    await this.repo.save(tool);
    return tool;
  }

  async getById(id: string): Promise<ToolsEntity> {
    const tool = await this.repo.findOne({ where: { id } });
    if (!tool) {
      throw new NotFoundException(`Tool ${id} not found`);
    }
    return tool;
  }

  async delete(id: string): Promise<{ success: boolean }> {
    await this.getById(id);
    await this.repo.deleteById(id);
    return { success: true };
  }

  async getAllAsMCPFunctions(userId: string) {
    const tools = (await this.getMany({filter: [`userId:eq:${userId}`]}, {})).data;

    const toolSet: Record<
      string,
      {
        description: string;
        parameters: ZodTypeAny;
        execute: (args: any, options: ToolExecutionOptions) => Promise<HttpExecutionResult | string[]>;
      }
    > = {};

    for (const tool of tools) {
      const setting = tool.setting as ToolSettingDto;

      // Combine all parameter types into a single schema
      const parameters = z.object({
        ...this.generateZodShape(setting.queryParams),
        ...this.generateZodShape(setting.bodyParams),
        ...this.generateZodShape(setting.headerParams),
        ...this.generateZodShape(setting.pathParams),
      });

      toolSet[tool.name] = {
        description: tool.description,
        parameters,
        execute: async (args: any, options: ToolExecutionOptions & { timeoutMs?: number }) => {
          try {
            // Validate input parameters
            const validatedArgs = parameters.parse(args);

            // Execute the HTTP request
            const result = await this.executeHttpRequest(setting, validatedArgs, options);

            this.logger.log(`Tool '${tool.name}' executed successfully`, {
              success: result.success,
              status: result.status,
              executionTime: result.executionTime,
            });

            return result;

          } catch (error) {
            this.logger.error(`Tool '${tool.name}' execution failed`, error);

            if (error instanceof z.ZodError) {
              return {
                success: false,
                error: `Parameter validation failed: ${error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')}`,
              };
            }

            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown execution error',
            };
          }
        },
      };
    }

    return toolSet;
  }

  async getToolSchema(id: string): Promise<{ name: string; description: string; schema: any }> {
    const tool = await this.getById(id);
    const setting = tool.setting as ToolSettingDto;

    const parameters = z.object({
      ...this.generateZodShape(setting.queryParams),
      ...this.generateZodShape(setting.bodyParams),
      ...this.generateZodShape(setting.headerParams),
      ...this.generateZodShape(setting.pathParams),
    });

    return {
      name: tool.name,
      description: tool.description,
      schema: parameters._def,
    };
  }
}