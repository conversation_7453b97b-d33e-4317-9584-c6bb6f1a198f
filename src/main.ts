import { ClassSerializerInterceptor, HttpStatus, UnprocessableEntityException, ValidationPipe } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import type { NestExpressApplication } from '@nestjs/platform-express';
import { ExpressAdapter } from '@nestjs/platform-express';
import compression from 'compression';
import morgan from 'morgan';
import { initializeTransactionalContext } from 'typeorm-transactional';

import { AppModule } from './app.module';
import { setupSwagger } from './setup-swagger';
import { ApiConfigService } from './shared/services/api-config.service';
import { SharedModule } from './shared/shared.module';
import { TransformInterceptor } from './interceptor/transform.interceptor';

async function bootstrap() {
  initializeTransactionalContext();

  const app = await NestFactory.create<NestExpressApplication>(AppModule, new ExpressAdapter(), {
    cors: true,
    bodyParser: false,
  });
  const configService = app.select(SharedModule).get(ApiConfigService);

  if (configService.isProduction) {
    app.useLogger(['log', 'warn', 'error']);
  }

  // Middleware setup
  app.enableCors({
    origin: '*',
    methods: 'GET,OPTION,PUT,PATCH,POST,DELETE',
    preflightContinue: true,
    optionsSuccessStatus: 204,
    credentials: true,
  });

  app.use(compression());
  app.use(
    morgan('combined', {
      skip: (req) => ['health', '/docs/', 'swagger'].some((path) => req.url?.includes(path)),
    }),
  );
  app.enableVersioning();
  app.enable('trust proxy', true);

  // Global Interceptors
  const reflector = app.get(Reflector);
  app.useGlobalInterceptors(new ClassSerializerInterceptor(reflector));
  // app.useGlobalFilters(new AllExceptionsFilter());
  app.useGlobalInterceptors(new TransformInterceptor());

  // Global Validation Pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      transform: true,
      dismissDefaultMessages: true,
      exceptionFactory: (errors) => new UnprocessableEntityException(errors),
    }),
  );

  // Swagger setup
  if (configService.documentationEnabled) {
    setupSwagger(app);
  }

  await app.listen(configService.appConfig.port);

  console.info(`Server is running on ${await app.getUrl()}`);
}

/* eslint-disable unicorn/prefer-top-level-await */
void bootstrap();
